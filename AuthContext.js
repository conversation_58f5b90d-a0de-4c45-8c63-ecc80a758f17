import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearRoleData } from './utils/storage';

const AuthContext = createContext();

const USER_STORAGE_KEY = 'USER_DATA_V1';

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [role, setRole] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user data from AsyncStorage on app start
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        setUser(userData);
        setRole(userData.role);
        console.log('Loaded user data from storage:', userData);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (userData) => {
    try {
      // Save to AsyncStorage
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
      
      // Update state
      setUser(userData);
      setRole(userData.role);
      
      console.log('User logged in and data saved:', userData);
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  };

  const logout = async () => {
    try {
      // Only clear user authentication data, NOT role-specific app data
      // This allows users to log back in and access their previous data
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      
      // Clear state
      setUser(null);
      setRole(null);
      
      console.log('User logged out but app data preserved for next login');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const updateUserData = async (updatedData) => {
    try {
      const newUserData = { ...user, ...updatedData };
      
      // Save to AsyncStorage
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(newUserData));
      
      // Update state
      setUser(newUserData);
      setRole(newUserData.role);
      
      console.log('User data updated:', newUserData);
    } catch (error) {
      console.error('Error updating user data:', error);
    }
  };

  if (isLoading) {
    // You can return a loading screen here if needed
    return null;
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      role, 
      login, 
      logout, 
      updateUserData,
      isLoading 
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
} 