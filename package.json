{"name": "trancpota", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:css": "tailwindcss -i ./global.css -o ./web-build/tailwind.css --watch"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "^53.0.15", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "~18.1.6", "expo-status-bar": "~2.2.3", "install": "^0.13.0", "leaflet": "^1.9.4", "nativewind": "^4.1.23", "postcss": "^8.5.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "2.24.0", "react-native-maps": "1.20.1", "react-native-reanimated": "3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^18.0.0", "babel-preset-expo": "13.0.0", "tailwindcss": "3.3.3"}, "private": true}