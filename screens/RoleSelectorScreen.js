import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Image } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';
import { LinearGradient } from 'expo-linear-gradient';

const roles = [
  {
    label: 'Sender/Receiver',
    icon: 'send',
    description: 'Send or receive packages quickly and securely.'
  },
  {
    label: 'Traveller',
    icon: 'map-pin',
    description: 'Earn by delivering packages along your route.'
  },
  {
    label: 'Agent',
    icon: 'shield',
    description: 'Act as a trusted third-party for secure transactions.'
  }
];

const RoleSelectorScreen = ({ navigation }) => {
  const navigateToLogin = (role) => {
    navigation.navigate('Login', { role });
  };

  return (
    <LinearGradient
      colors={["#e0e7ff", "#f1f5f9"]}
      style={{ flex: 1 }}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="flex-1 px-4">
        <View className="w-full max-w-lg mx-auto pt-14 pb-10">
          {/* Top Branding Section */}
          <View className="items-center mb-10">
            <Image source={require('../assets/icon.png')} style={{ width: 64, height: 64, marginBottom: 12 }} resizeMode="contain" />
            <Text className="text-3xl font-extrabold text-slate-900 tracking-tight mb-1">Welcome to Trancpota</Text>
            <Text className="text-base text-slate-500 text-center max-w-xs">Select your role to get started with a seamless delivery experience.</Text>
          </View>
          {/* Roles Section */}
          <View className="space-y-7">
            {roles.map((role) => (
              <TouchableOpacity
                key={role.label}
                onPress={() => navigateToLogin(role.label)}
                className="flex-row items-center bg-white rounded-2xl py-6 px-6 w-full shadow-md border border-slate-100 mb-2"
                activeOpacity={0.92}
                accessible accessibilityRole="button" accessibilityLabel={role.label}
              >
                <View className="bg-indigo-50 rounded-xl p-5 mr-6 items-center justify-center">
                  <Feather name={role.icon} size={30} color="#6366f1" />
                </View>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-slate-900 mb-1">
                    {role.label}
                  </Text>
                  <Text className="text-base text-slate-500 leading-6">
                    {role.description}
                  </Text>
                </View>
                <Feather name="chevron-right" size={26} color="#a5b4fc" style={{ marginLeft: 8 }} />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

export default RoleSelectorScreen; 