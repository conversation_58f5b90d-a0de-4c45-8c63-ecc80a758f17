import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';

const NotificationsScreen = ({ navigation }) => {
  const handleBack = () => {
    if (navigation && navigation.canGoBack && navigation.canGoBack()) {
      navigation.goBack();
    } else if (navigation && navigation.navigate) {
      navigation.navigate('Home');
    }
  };

  return (
    <View className="flex-1 bg-white justify-center items-center px-6">
      {/* Back to Home Icon */}
      <TouchableOpacity
        onPress={handleBack}
        style={{ position: 'absolute', top: 36, left: 20, zIndex: 10 }}
        accessible accessibilityRole="button" accessibilityLabel="Back to Home"
      >
        <Feather name="arrow-left" size={28} color="#6366f1" />
      </TouchableOpacity>
      <Feather name="bell" size={48} color="#6366f1" className="mb-4" />
      <Text className="text-2xl font-bold text-slate-900 mb-2">Notifications</Text>
      <Text className="text-base text-slate-600 text-center">You have no notifications yet.</Text>
    </View>
  );
};

export default NotificationsScreen; 