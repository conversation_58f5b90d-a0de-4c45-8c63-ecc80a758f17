import React, { useState, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, Platform, KeyboardAvoidingView, Modal, FlatList, Animated, PanResponder } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';

const COUNTRIES = [
  { name: 'Uganda', code: '+256' },
  { name: 'Kenya', code: '+254' },
  { name: 'Tanzania', code: '+255' },
  { name: 'Rwanda', code: '+250' },
  { name: 'South Sudan', code: '+211' },
];

const SLIDER_WIDTH = 280;
const SLIDER_HEIGHT = 54;
const HANDLE_SIZE = 48;

const LoginScreen = ({ route, navigation }) => {
  const { role } = route.params;
  const [username, setUsername] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [focused, setFocused] = useState(false);
  const [country, setCountry] = useState(COUNTRIES[0]);
  const [modalVisible, setModalVisible] = useState(false);

  const handleGetOtp = () => {
    if (username.length > 0 && phoneNumber.length > 0) {
      navigation.navigate('Otp', { role, username, phoneNumber: country.code + phoneNumber });
    }
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 justify-center items-center px-6"
      style={{ backgroundColor: '#f1f5f9' }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      {/* Top Section */}
      <View className="items-center mb-12 mt-8">
        <View className="w-20 h-20 rounded-full bg-green-100 items-center justify-center mb-5 shadow-md">
          <Feather name="message-circle" size={40} color="#22c55e" />
        </View>
        <Text className="text-3xl font-extrabold text-slate-900 mb-2 tracking-tight">Sign in to continue</Text>
        <Text className="text-base font-medium text-slate-500 mb-1 uppercase tracking-wide">{role}</Text>
        <Text className="text-base text-slate-500 text-center max-w-xs leading-relaxed">We'll send you an SMS to verify your number.</Text>
      </View>
      
      {/* Username Input Section */}
      <View className="w-full max-w-[370px] mb-6">
        <Text className="text-xs font-bold text-slate-500 mb-2 uppercase tracking-wider">Username</Text>
        <View className={`bg-white rounded-2xl border shadow-md ${focused ? 'border-green-500' : 'border-slate-200'} px-3 py-4`}>
          <TextInput
            placeholder="Enter your username"
            className="text-base text-slate-900 tracking-wider font-medium"
            value={username}
            onChangeText={setUsername}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholderTextColor="#bbb"
            returnKeyType="next"
          />
        </View>
      </View>
      
      {/* Phone Input Section */}
      <View className="w-full max-w-[370px] mb-8">
        <Text className="text-xs font-bold text-slate-500 mb-2 uppercase tracking-wider">Phone Number</Text>
        <View className={`flex-row items-center bg-white rounded-2xl border shadow-md ${focused ? 'border-green-500' : 'border-slate-200'} px-3 py-4`}>
          {/* Inline Country Selector */}
          <TouchableOpacity
            className="flex-row items-center rounded-md border border-slate-200 bg-slate-50 px-2 py-2 mr-2 min-w-[110px]"
            onPress={() => setModalVisible(true)}
            activeOpacity={0.8}
            style={{ flexShrink: 0 }}
          >
            <Feather name="globe" size={16} color="#22c55e" className="mr-1" />
            <Text className="text-base text-slate-900 mr-1 font-semibold">{country.code}</Text>
            <Feather name="chevron-down" size={16} color="#888" />
          </TouchableOpacity>
          {/* Phone Input */}
          <TextInput
            placeholder="712 345 678"
            className="flex-1 text-base text-slate-900 tracking-wider font-medium"
            keyboardType="phone-pad"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            maxLength={15}
            placeholderTextColor="#bbb"
            returnKeyType="done"
          />
        </View>
        {/* Country Modal remains unchanged */}
        <Modal
          visible={modalVisible}
          transparent
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableOpacity className="flex-1 bg-black/30 justify-center items-center" activeOpacity={1} onPressOut={() => setModalVisible(false)}>
            <View className="bg-white rounded-xl w-80 max-w-[90vw] p-4 shadow-lg">
              <Text className="text-xl font-bold mb-3 text-slate-900 tracking-tight">Select Country</Text>
              <FlatList
                data={COUNTRIES}
                keyExtractor={item => item.name}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    className="flex-row items-center py-3 px-2 rounded-lg hover:bg-slate-100"
                    onPress={() => {
                      setCountry(item);
                      setModalVisible(false);
                    }}
                  >
                    <Text className="text-base text-slate-900 flex-1 font-medium">{item.name}</Text>
                    <Text className="text-green-500 font-bold ml-2">{item.code}</Text>
                  </TouchableOpacity>
                )}
                ItemSeparatorComponent={() => <View className="h-px bg-slate-100" />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
      {/* Next Button */}
      <View className="w-full max-w-xs items-center mb-1">
        <TouchableOpacity
          className={`w-full py-4 rounded-full items-center justify-center ${username.length >= 3 && phoneNumber.length >= 8 ? 'bg-green-500' : 'bg-green-200'}`}
          disabled={username.length < 3 || phoneNumber.length < 8}
          onPress={handleGetOtp}
          activeOpacity={username.length >= 3 && phoneNumber.length >= 8 ? 0.85 : 1}
        >
          <Text className={`text-lg font-bold ${username.length >= 3 && phoneNumber.length >= 8 ? 'text-white' : 'text-green-400'}`}>Next</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen; 