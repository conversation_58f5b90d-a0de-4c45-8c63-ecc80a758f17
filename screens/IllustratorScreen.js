import React, { useState, useRef } from 'react';
import { View, FlatList, TouchableOpacity, Text, useWindowDimensions } from 'react-native';
import IllustratorStep from '../components/IllustratorStep';
import { SafeAreaView } from 'react-native-safe-area-context';
import { generateUser } from './sender/senderReceiverData';

const steps = {
  'Sender/Receiver': [
    { title: 'Step 1: Post a Job', description: 'Easily post a job detailing what you need to send.' },
    { title: 'Step 2: Get Matched', description: 'Our platform matches you with a reliable traveller.' },
    { title: 'Step 3: Track Your Delivery', description: 'Track your package in real-time until it arrives safely.' },
  ],
  'Traveller': [
    { title: 'Step 1: Find Jobs', description: 'Browse delivery jobs that match your travel route.' },
    { title: 'Step 2: Accept a Job', description: 'Accept jobs that fit your schedule and earn money.' },
    { title: 'Step 3: Deliver and Get Paid', description: 'Deliver the package and receive your payment instantly.' },
  ],
  'Agent': [
    { title: 'Step 1: Verify Transactions', description: 'Act as a trusted third-party to verify transactions.' },
    { title: 'Step 2: Hold Funds Securely', description: 'Hold funds in escrow until the delivery is complete.' },
    { title: 'Step 3: Release Payment', description: 'Release the payment to the traveller upon successful delivery.' },
  ],
};

const stepIcons = {
  'Sender/Receiver': ['plus-circle', 'users', 'map-pin'],
  'Traveller': ['search', 'check-circle', 'dollar-sign'],
  'Agent': ['shield', 'lock', 'credit-card'],
};

const IllustratorScreen = ({ route, onLogin }) => {
  const { role, phoneNumber } = route.params;
  const { width } = useWindowDimensions();
  const roleSteps = steps[role] || [];
  const roleIcons = stepIcons[role] || [];
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  const handleNext = () => {
    const nextIndex = currentIndex + 1;
    if (nextIndex < roleSteps.length) {
      flatListRef.current.scrollToIndex({ index: nextIndex, animated: true });
      setCurrentIndex(nextIndex);
    } else {
      // Generate a mock user code for now (replace with backend integration)
      const mockUserNumber = Math.floor(Math.random() * 1000) + 1;
      const userCode = generateUser(mockUserNumber).id;
      onLogin(role, userCode, phoneNumber);
    }
  };

  const getItemLayout = (data, index) => ({
    length: width,
    offset: width * index,
    index,
  });

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1 justify-center items-center bg-white">
        <FlatList
          ref={flatListRef}
          data={roleSteps}
          renderItem={({ item, index }) => (
            <IllustratorStep
              title={item.title}
              description={item.description}
              icon={roleIcons[index]}
            />
          )}
          keyExtractor={(item, index) => index.toString()}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          scrollEnabled={false}
          getItemLayout={getItemLayout}
          style={{ flexGrow: 0 }}
        />
      </View>
      {/* Floating bottom bar */}
      <View className="absolute left-0 right-0 bottom-0 px-0 pb-6 pt-2 z-20 items-center" style={{ alignItems: 'center' }}>
        <View className="flex-row justify-between items-center bg-white rounded-2xl shadow-lg px-6 py-4 w-[96vw] max-w-xl mx-auto">
          <View className="flex-row">
            {roleSteps.map((_, index) => (
              <View
                key={index}
                className={`w-3 h-3 rounded-full mx-1 ${index === currentIndex ? 'bg-indigo-500' : 'bg-slate-300'}`}
              />
            ))}
          </View>
          <TouchableOpacity
            className="bg-indigo-500 px-7 py-3 rounded-xl shadow-md ml-4"
            onPress={handleNext}
            activeOpacity={0.88}
          >
            <Text className="text-white text-base font-bold">{currentIndex === roleSteps.length - 1 ? 'Get Started' : 'Next'}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default IllustratorScreen; 