import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Platform, KeyboardAvoidingView, ActivityIndicator } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../AuthContext';

const OtpScreen = ({ route, navigation }) => {
  const { role, username, phoneNumber } = route.params;
  const { login } = useAuth();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [focused, setFocused] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const inputRefs = useRef([]);
  const [resendTimer, setResendTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    inputRefs.current[0]?.focus();
    setCanResend(false);
    setResendTimer(30);
    const interval = setInterval(() => {
      setResendTimer(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleOtpChange = (value, index) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e, index) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOtp = async () => {
    const otpString = otp.join('');
    if (otpString.length !== 6 || otp.includes('')) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate OTP verification
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate unique user code
      const userCode = generateUserCode();
      
      // Store user data
      const userData = {
        role,
        username,
        userCode,
        phoneNumber,
        isLoggedIn: true,
        loginTime: new Date().toISOString()
      };

      await AsyncStorage.setItem('userData', JSON.stringify(userData));
      console.log('User data stored:', userData);
      // Use context login to update global auth state
      login(userData);
    } catch (error) {
      console.error('Error during OTP verification:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generateUserCode = () => {
    const chars = 'ACDEFHJKLMNPQRTVWXY';
    let code = '';
    for (let i = 0; i < 5; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `SR:${code}`;
  };

  const isOtpComplete = otp.every(digit => digit !== '');

  return (
    <KeyboardAvoidingView
      className="flex-1 justify-center items-center px-6"
      style={{ backgroundColor: '#f1f5f9' }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      {/* Top Section */}
      <View className="items-center mb-12 mt-8">
        <View className="w-20 h-20 rounded-full bg-green-100 items-center justify-center mb-5 shadow-md">
          <Feather name="shield" size={40} color="#22c55e" />
        </View>
        <Text className="text-3xl font-extrabold text-slate-900 mb-2 tracking-tight">Verify your number</Text>
        <Text className="text-base font-medium text-slate-500 mb-1 uppercase tracking-wide">{role}</Text>
        <Text className="text-base text-slate-500 text-center max-w-xs leading-relaxed">
          We've sent a 6-digit code to <Text className="font-semibold text-slate-900">{phoneNumber}</Text>
        </Text>
      </View>

      {/* OTP Input Section */}
      <View className="w-full max-w-[370px] mb-8">
        <Text className="text-xs font-bold text-slate-500 mb-2 uppercase tracking-wider">Enter OTP</Text>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 10 }}>
          {otp.map((digit, index) => (
            <View
              key={index}
              style={{
                width: 48,
                height: 64,
                backgroundColor: '#fff',
                borderRadius: 16,
                borderWidth: 2,
                borderColor: focused ? '#22c55e' : '#e5e7eb',
                shadowColor: '#000',
                shadowOpacity: 0.06,
                shadowRadius: 4,
                shadowOffset: { width: 0, height: 2 },
                alignItems: 'center',
                justifyContent: 'center',
                marginHorizontal: 2,
              }}
            >
              <TextInput
                ref={ref => (inputRefs.current[index] = ref)}
                style={{
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#0f172a',
                  textAlign: 'center',
                  width: '100%',
                  height: '100%',
                  fontFamily: Platform.OS === 'web' ? 'monospace' : 'monospace',
                  letterSpacing: 2,
                  backgroundColor: 'transparent',
                  padding: 0,
                  margin: 0,
                }}
                value={digit}
                onChangeText={value => handleOtpChange(value, index)}
                onKeyPress={e => handleKeyPress(e, index)}
                onFocus={() => setFocused(true)}
                onBlur={() => setFocused(false)}
                keyboardType="numeric"
                maxLength={1}
                returnKeyType="next"
              />
            </View>
          ))}
        </View>
      </View>

      {/* Error Message */}
      {error ? (
        <View className="w-full max-w-[370px] mb-6">
          <Text className="text-red-500 text-center text-sm font-medium">{error}</Text>
        </View>
      ) : null}

      {/* Verify Button */}
      <View className="w-full max-w-xs items-center mb-1">
        <TouchableOpacity
          className={`w-full py-4 rounded-full items-center justify-center ${
            isOtpComplete && !loading ? 'bg-green-500' : 'bg-green-200'
          }`}
          disabled={!isOtpComplete || loading}
          onPress={handleVerifyOtp}
          activeOpacity={isOtpComplete && !loading ? 0.85 : 1}
        >
          {loading ? (
            <View className="flex-row items-center">
              <ActivityIndicator size="small" color="#fff" />
              <Text className="text-lg font-bold text-white ml-2">Verifying...</Text>
            </View>
          ) : (
            <Text className={`text-lg font-bold ${isOtpComplete ? 'text-white' : 'text-green-400'}`}>
              Verify OTP
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Resend OTP */}
      <View className="w-full max-w-xs items-center mt-4">
        {canResend ? (
          <TouchableOpacity className="py-2" activeOpacity={0.7} onPress={() => {
            setCanResend(false);
            setResendTimer(30);
            // TODO: trigger resend OTP logic here
          }}>
            <Text className="text-green-500 text-base font-semibold">Resend OTP</Text>
          </TouchableOpacity>
        ) : (
          <Text className="text-slate-400 text-base font-semibold">Resend in {resendTimer}s</Text>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default OtpScreen;