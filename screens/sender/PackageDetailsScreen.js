import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Alert, Platform, Dimensions, ScrollView } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import { stations } from './stationsData';
import { receivers } from './receiversData';
import DeliveryMapView from './DeliveryMapView';

const PackageDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { packageData, mode } = route.params;
  const [destinationStation, setDestinationStation] = useState(null);
  const [receiverInfo, setReceiverInfo] = useState(null);
  const [distance, setDistance] = useState(null);
  const [duration, setDuration] = useState(null);

  // Get station and receiver information
  useEffect(() => {
    if (packageData) {
      const station = stations.find(s => s.id === packageData.receiverStationId);
      const receiver = receivers.find(r => r.id === packageData.receiverId);
      
      setDestinationStation(station);
      setReceiverInfo(receiver);
    }
  }, [packageData]);

  // Calculate distance and duration (simplified)
  useEffect(() => {
    if (destinationStation) {
      // Mock distance calculation - in real app, use actual location services
      const mockDistance = Math.floor(Math.random() * 10) + 1; // 1-10 km
      const mockDuration = Math.floor(Math.random() * 30) + 10; // 10-40 minutes
      
      setDistance(mockDistance);
      setDuration(mockDuration);
    }
  }, [destinationStation]);

  const handleNavigate = () => {
    if (destinationStation) {
      // In a real app, this would open the device's navigation app
      Alert.alert(
        'Navigate to Station',
        `Open navigation to ${destinationStation.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Navigate', 
            onPress: () => {
              // Here you would integrate with actual navigation apps
              Alert.alert('Navigation', 'Opening navigation app...');
            }
          }
        ]
      );
    }
  };

  const handleCallStation = () => {
    if (destinationStation && destinationStation.phone) {
      Alert.alert(
        'Call Station',
        `Call ${destinationStation.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Call', 
            onPress: () => {
              // Here you would integrate with phone dialer
              Alert.alert('Call', `Calling ${destinationStation.phone}...`);
            }
          }
        ]
      );
    }
  };

  if (!packageData || !destinationStation) {
    return (
      <View className="flex-1 justify-center items-center bg-slate-50">
        <Text className="text-lg text-red-500">Package or station not found.</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-slate-100">
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: Platform.OS === 'web' ? 32 : 16,
        paddingBottom: 16,
        paddingHorizontal: 20,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: 12, padding: 6 }}>
          <Feather name="arrow-left" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1e293b', flex: 1 }}>
          Package Details
        </Text>
      </View>

      {/* Map Container (refactored) */}
      <DeliveryMapView station={destinationStation}>
        {/* Floating Package Card */}
        <View className="absolute top-4 left-4 right-4 bg-white rounded-xl shadow-lg border border-slate-200 p-4">
          {/* Package ID - Prominently Displayed */}
          <View className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3 items-center">
            <Text className="text-xs text-blue-600 font-medium mb-1">PACKAGE ID (Show at Station)</Text>
            <Text style={{ fontSize: 36, fontWeight: 'bold', color: '#1e40af', letterSpacing: 2 }}>
              {packageData.id.slice(-4)}
            </Text>
          </View>

          {/* Package Info */}
          <View className="flex-row justify-between items-start mb-3">
            <View className="flex-1">
              <Text className="text-base font-bold text-slate-900">{packageData.name}</Text>
              <Text className="text-sm text-slate-600">{packageData.weight} • {packageData.type}</Text>
            </View>
            <View className="bg-green-100 px-2 py-1 rounded-full">
              <Text className="text-xs font-semibold text-green-700">Active</Text>
            </View>
          </View>

          {/* Station Info */}
          <View className="border-t border-slate-200 pt-3 flex-row items-center justify-between">
            <View style={{ flex: 1 }}>
              <Text className="text-sm font-semibold text-slate-700 mb-1">Drop-off Station</Text>
              <Text className="text-base font-medium text-slate-900">{destinationStation.name}</Text>
              <Text className="text-sm text-slate-600">{destinationStation.address}</Text>
              {distance && duration && (
                <Text className="text-sm text-slate-500 mt-1">
                  📍 {distance}km away • ⏱️ {duration} min
                </Text>
              )}
            </View>
            {/* Call Icon/Button */}
            <TouchableOpacity onPress={handleCallStation} style={{ marginLeft: 12, padding: 8 }}>
              <Feather name="phone" size={22} color="#10b981" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="absolute bottom-6 left-4 right-4">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              onPress={handleNavigate}
              className="flex-1 bg-blue-600 rounded-xl py-4 flex-row items-center justify-center"
            >
              <Feather name="navigation" size={20} color="white" />
              <Text className="text-white font-semibold ml-2">Navigate</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={() => navigation.navigate('AddPackage', { 
                mode: 'edit', 
                packageData: packageData,
                stationId: packageData.receiverStationId
              })}
              className="bg-slate-200 rounded-xl py-4 px-4 flex-row items-center justify-center"
            >
              <Feather name="edit-2" size={20} color="#374151" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Receiver Info Card */}
        {receiverInfo && (
          <View className="absolute bottom-24 left-4 right-4 bg-white rounded-xl shadow-lg border border-slate-200 p-4">
            <Text className="text-sm font-semibold text-slate-700 mb-2">Receiver Information</Text>
            <View className="flex-row items-center">
              <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
                <Feather name="user" size={16} color="#3b82f6" />
              </View>
              <View className="flex-1">
                <Text className="text-base font-medium text-slate-900">{receiverInfo.name}</Text>
                <Text className="text-sm text-slate-600">{receiverInfo.email}</Text>
                {receiverInfo.phone && (
                  <Text className="text-sm text-slate-600">{receiverInfo.phone}</Text>
                )}
              </View>
            </View>
          </View>
        )}
      </DeliveryMapView>
    </View>
  );
};

export default PackageDetailsScreen; 