import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Platform, TextInput, Alert, StyleSheet, SafeAreaView } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userStorage, userPackageStorage } from '../../utils/storage';

const AVATAR_PLACEHOLDER = 'https://ui-avatars.com/api/?name=User&background=6366f1&color=fff&size=128';
const ID_PLACEHOLDER = 'https://cdn.jsdelivr.net/gh/ghkatende/cdn/id-placeholder.png';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({ sent: 0, received: 0 });
  const [editMode, setEditMode] = useState(false);
  const [editData, setEditData] = useState({ fullname: '', username: '', location: '', name: '', phoneNumber: '', email: '', idFront: '', idBack: '' });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      const u = await userStorage.getUser();
      setUser(u);
      if (u) {
        const sent = (await userPackageStorage.getPackages(u.userCode, u.role)).filter(pkg => pkg.senderId === u.userCode).length;
        const received = (await userPackageStorage.getPackages(u.userCode, u.role)).filter(pkg => pkg.receiverId === u.userCode).length;
        setStats({ sent, received });
        setEditData({
          fullname: u.fullname || '',
          username: u.username || '',
          location: u.location || '',
          name: u.name || '',
          phoneNumber: u.phoneNumber || '',
          email: u.email || '',
          idFront: u.idFront || '',
          idBack: u.idBack || '',
        });
      }
    };
    loadUser();
  }, []);

  const handleEdit = () => {
    setEditData({
      fullname: user?.fullname || '',
      username: user?.username || '',
      location: user?.location || '',
      name: user?.name || '',
      phoneNumber: user?.phoneNumber || '',
      email: user?.email || '',
      idFront: user?.idFront || '',
      idBack: user?.idBack || '',
    });
    setEditMode(true);
  };

  const handleCancel = () => {
    setEditMode(false);
  };

  const handleSave = async () => {
    if (!editData.fullname.trim() || !editData.username.trim() || !editData.location.trim() || !editData.name.trim() || !editData.phoneNumber.trim()) {
      Alert.alert('Validation', 'Fullname, Username, Location, Name, and Phone number are required.');
      return;
    }
    setSaving(true);
    try {
      const updatedUser = { ...user, ...editData };
      await userStorage.setUser(updatedUser);
      setUser(updatedUser);
      setEditMode(false);
    } catch (e) {
      Alert.alert('Error', 'Failed to save profile.');
    }
    setSaving(false);
  };

  // Image picker for ID upload
  const pickImage = async (side) => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.7,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setEditData({ ...editData, [side]: result.assets[0].uri });
    }
  };

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <Feather name="user" size={48} color="#6366f1" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      {/* Gradient Header with Avatar */}
      <LinearGradient colors={["#6366f1", "#818cf8", "#f1f5f9"]} start={{x:0,y:0}} end={{x:1,y:1}} style={styles.headerGradient}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerIconBtn}>
            <Feather name="arrow-left" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          {!editMode ? (
            <TouchableOpacity onPress={handleEdit} style={styles.headerIconBtn}>
              <Feather name="edit-2" size={22} color="#fff" />
            </TouchableOpacity>
          ) : (
            <View style={{ width: 32 }} />
          )}
        </View>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: user.avatarUrl || AVATAR_PLACEHOLDER }} style={styles.avatar} />
        </View>
      </LinearGradient>

      <ScrollView contentContainerStyle={{ paddingBottom: 40 }} style={{ flex: 1 }}>
        {/* Profile Card */}
        <View style={styles.profileCard}>
          {editMode ? (
            <>
              <ProfileInput icon="account" placeholder="Full Name" value={editData.fullname} onChangeText={text => setEditData({ ...editData, fullname: text })} editable={!saving} />
              <ProfileInput icon="at" placeholder="Username" value={editData.username} onChangeText={text => setEditData({ ...editData, username: text })} editable={!saving} />
              <ProfileInput icon="map-marker" placeholder="Location" value={editData.location} onChangeText={text => setEditData({ ...editData, location: text })} editable={!saving} />
              <ProfileInput icon="badge-account-horizontal" placeholder="Display Name" value={editData.name} onChangeText={text => setEditData({ ...editData, name: text })} editable={!saving} />
              <ProfileInput icon="phone" placeholder="Phone Number" value={editData.phoneNumber} onChangeText={text => setEditData({ ...editData, phoneNumber: text })} keyboardType="phone-pad" editable={!saving} />
              <ProfileInput icon="email" placeholder="Email (optional)" value={editData.email} onChangeText={text => setEditData({ ...editData, email: text })} keyboardType="email-address" editable={!saving} />
            </>
          ) : (
            <>
              <Text style={styles.fullname}>{user.fullname || '-'}</Text>
              <Text style={styles.username}>@{user.username || '-'}</Text>
              <View style={styles.locationRow}>
                <Feather name="map-pin" size={16} color="#6366f1" style={{ marginRight: 4 }} />
                <Text style={styles.locationText}>{user.location || '-'}</Text>
              </View>
              <View style={styles.badgeRow}>
                <Text style={styles.roleBadge}>{user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'Sender'}</Text>
                <Text style={styles.idBadge}>ID: {user.userCode}</Text>
              </View>
              <View style={styles.contactRow}>
                <Feather name="phone" size={16} color="#6366f1" style={{ marginRight: 6 }} />
                <Text style={styles.contactText}>{user.phoneNumber}</Text>
                {user.email ? <><Feather name="mail" size={16} color="#6366f1" style={{ marginLeft: 16, marginRight: 6 }} /><Text style={styles.contactText}>{user.email}</Text></> : null}
              </View>
              <Text style={styles.displayName}>{user.name || ''}</Text>
            </>
          )}

          {/* Stats */}
          <View style={styles.statsRow}>
            <View style={styles.statBox}>
              <Feather name="send" size={20} color="#6366f1" />
              <Text style={styles.statValue}>{stats.sent}</Text>
              <Text style={styles.statLabel}>Sent</Text>
            </View>
            <View style={styles.statBox}>
              <Feather name="inbox" size={20} color="#10b981" />
              <Text style={styles.statValue}>{stats.received}</Text>
              <Text style={styles.statLabel}>Received</Text>
            </View>
          </View>
        </View>

        {/* ID Verification Section */}
        <View style={styles.sectionDivider} />
        <Text style={styles.sectionTitle}>ID Verification</Text>
        <View style={styles.idRow}>
          <View style={styles.idCardBox}>
            <TouchableOpacity disabled={!editMode || saving} onPress={() => pickImage('idFront')} style={{ alignItems: 'center' }}>
              <Image source={{ uri: (editMode ? editData.idFront : user.idFront) || ID_PLACEHOLDER }} style={styles.idImage} />
              <Text style={styles.idLabel}>ID Front</Text>
              {editMode && <Feather name="upload" size={18} color="#6366f1" style={styles.idUploadIcon} />}
            </TouchableOpacity>
          </View>
          <View style={styles.idCardBox}>
            <TouchableOpacity disabled={!editMode || saving} onPress={() => pickImage('idBack')} style={{ alignItems: 'center' }}>
              <Image source={{ uri: (editMode ? editData.idBack : user.idBack) || ID_PLACEHOLDER }} style={styles.idImage} />
              <Text style={styles.idLabel}>ID Back</Text>
              {editMode && <Feather name="upload" size={18} color="#6366f1" style={styles.idUploadIcon} />}
            </TouchableOpacity>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.sectionDivider} />
        <TouchableOpacity
          style={styles.travellerBtn}
          onPress={() => navigation.navigate('RoleSelector')}
          activeOpacity={0.85}
          disabled={editMode}
        >
          <Feather name="repeat" size={20} color="#eab308" />
          <Text style={styles.travellerBtnText}>Switch to Traveller Mode</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.logoutBtn}
          onPress={() => navigation.navigate('Logout')}
          activeOpacity={0.85}
          disabled={editMode}
        >
          <Feather name="log-out" size={20} color="#ef4444" />
          <Text style={styles.logoutBtnText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Edit Mode Save/Cancel Floating Bar */}
      {editMode && (
        <View style={styles.editBar}>
          <TouchableOpacity
            onPress={handleSave}
            style={[styles.saveBtn, saving && { opacity: 0.7 }]}
            disabled={saving}
          >
            <Text style={styles.saveBtnText}>Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            style={styles.cancelBtn}
            disabled={saving}
          >
            <Text style={styles.cancelBtnText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

// ProfileInput: rounded, filled, with icon
const ProfileInput = ({ icon, ...props }) => (
  <View style={styles.inputRow}>
    <MaterialCommunityIcons name={icon} size={20} color="#6366f1" style={{ marginRight: 10 }} />
    <TextInput
      style={styles.input}
      placeholderTextColor="#a5b4fc"
      {...props}
    />
  </View>
);

const styles = StyleSheet.create({
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f1f5f9' },
  loadingText: { fontSize: 18, color: '#a5b4fc', marginTop: 16 },
  headerGradient: { height: 170, borderBottomLeftRadius: 32, borderBottomRightRadius: 32, paddingHorizontal: 0, justifyContent: 'flex-end', position: 'relative' },
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 18, paddingTop: Platform.OS === 'web' ? 32 : 16, marginBottom: 0 },
  headerTitle: { color: '#fff', fontSize: 22, fontWeight: 'bold', letterSpacing: 1, flex: 1, textAlign: 'center' },
  headerIconBtn: { padding: 6, borderRadius: 999, backgroundColor: 'rgba(99,102,241,0.15)' },
  avatarContainer: { alignItems: 'center', position: 'absolute', left: 0, right: 0, bottom: -48 },
  avatar: { width: 96, height: 96, borderRadius: 48, borderWidth: 4, borderColor: '#fff', backgroundColor: '#e0e7ef', alignSelf: 'center', shadowColor: '#6366f1', shadowOpacity: 0.15, shadowRadius: 8, shadowOffset: { width: 0, height: 4 }, elevation: 4 },
  profileCard: { backgroundColor: '#fff', borderRadius: 24, marginTop: 64, marginHorizontal: 18, padding: 22, shadowColor: '#6366f1', shadowOpacity: 0.08, shadowRadius: 8, shadowOffset: { width: 0, height: 4 }, elevation: 2 },
  fullname: { fontSize: 22, fontWeight: 'bold', color: '#1e293b', textAlign: 'center', marginBottom: 2 },
  username: { fontSize: 15, color: '#6366f1', textAlign: 'center', marginBottom: 2 },
  locationRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 6 },
  locationText: { fontSize: 14, color: '#64748b' },
  badgeRow: { flexDirection: 'row', justifyContent: 'center', marginBottom: 8 },
  roleBadge: { backgroundColor: '#e0e7ff', color: '#6366f1', fontWeight: 'bold', fontSize: 12, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 4, marginRight: 8 },
  idBadge: { backgroundColor: '#f1f5f9', color: '#64748b', fontWeight: 'bold', fontSize: 12, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 4 },
  contactRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 2, marginTop: 2 },
  contactText: { fontSize: 15, color: '#334155' },
  displayName: { fontSize: 14, color: '#a5b4fc', textAlign: 'center', marginTop: 2, marginBottom: 2 },
  statsRow: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 18, marginBottom: 2 },
  statBox: { alignItems: 'center', flex: 1 },
  statValue: { fontSize: 20, fontWeight: 'bold', color: '#6366f1', marginTop: 2 },
  statLabel: { fontSize: 13, color: '#64748b', marginTop: 2 },
  sectionDivider: { height: 1, backgroundColor: '#e5e7eb', marginVertical: 24, marginHorizontal: 18, borderRadius: 2 },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: '#1e293b', marginLeft: 28, marginBottom: 10 },
  idRow: { flexDirection: 'row', justifyContent: 'center', marginBottom: 8 },
  idCardBox: { alignItems: 'center', marginHorizontal: 12 },
  idImage: { width: 90, height: 60, borderRadius: 12, borderWidth: 2, borderColor: '#e0e7ef', marginBottom: 6, backgroundColor: '#f1f5f9' },
  idLabel: { fontSize: 13, color: '#64748b', marginTop: 2 },
  idUploadIcon: { position: 'absolute', bottom: 8, right: 8, backgroundColor: '#fff', borderRadius: 999, padding: 2 },
  travellerBtn: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: '#fef9c3', borderColor: '#fde68a', borderWidth: 1, borderRadius: 16, paddingVertical: 14, marginHorizontal: 32, marginTop: 10, marginBottom: 10 },
  travellerBtnText: { color: '#eab308', fontWeight: 'bold', fontSize: 16, marginLeft: 8 },
  logoutBtn: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: '#fef2f2', borderColor: '#fecaca', borderWidth: 1, borderRadius: 16, paddingVertical: 14, marginHorizontal: 32, marginBottom: 30 },
  logoutBtnText: { color: '#ef4444', fontWeight: 'bold', fontSize: 16, marginLeft: 8 },
  editBar: { position: 'absolute', left: 0, right: 0, bottom: 0, backgroundColor: '#fff', flexDirection: 'row', justifyContent: 'space-between', padding: 18, borderTopWidth: 1, borderTopColor: '#e5e7eb', zIndex: 20 },
  saveBtn: { backgroundColor: '#6366f1', borderRadius: 12, paddingVertical: 12, paddingHorizontal: 36, marginRight: 10 },
  saveBtnText: { color: '#fff', fontWeight: 'bold', fontSize: 16 },
  cancelBtn: { backgroundColor: '#e5e7eb', borderRadius: 12, paddingVertical: 12, paddingHorizontal: 28 },
  cancelBtnText: { color: '#334155', fontWeight: 'bold', fontSize: 16 },
  inputRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#f1f5f9', borderRadius: 12, paddingHorizontal: 14, marginBottom: 12, borderWidth: 1, borderColor: '#e0e7ef' },
  input: { flex: 1, fontSize: 16, color: '#1e293b', paddingVertical: 12, backgroundColor: 'transparent' },
});

export default ProfileScreen; 