import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Platform, TextInput, Alert, StyleSheet, SafeAreaView, StatusBar, Dimensions } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userStorage, userPackageStorage } from '../../utils/storage';
import { useAuth } from '../../AuthContext';

const { width: screenWidth } = Dimensions.get('window');
const AVATAR_PLACEHOLDER = 'https://ui-avatars.com/api/?name=User&background=6366f1&color=fff&size=128';
const ID_PLACEHOLDER = 'https://cdn.jsdelivr.net/gh/ghkatende/cdn/id-placeholder.png';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { logout } = useAuth();
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({ sent: 0, received: 0, totalValue: 0 });
  const [editMode, setEditMode] = useState(false);
  const [editData, setEditData] = useState({ fullname: '', username: '', location: '', name: '', phoneNumber: '', email: '', idFront: '', idBack: '' });
  const [saving, setSaving] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      const u = await userStorage.getUser();
      setUser(u);
      if (u) {
        const packages = await userPackageStorage.getPackages(u.userCode, u.role);
        const sent = packages.filter(pkg => pkg.senderId === u.userCode).length;
        const received = packages.filter(pkg => pkg.receiverId === u.userCode).length;
        const totalValue = packages.reduce((sum, pkg) => sum + (pkg.value || 0), 0);

        setStats({ sent, received, totalValue });
        setIsVerified(u.idFront && u.idBack);
        setEditData({
          fullname: u.fullname || '',
          username: u.username || '',
          location: u.location || '',
          name: u.name || '',
          phoneNumber: u.phoneNumber || '',
          email: u.email || '',
          idFront: u.idFront || '',
          idBack: u.idBack || '',
        });
      }
    };
    loadUser();
  }, []);

  const handleEdit = () => {
    setEditData({
      fullname: user?.fullname || '',
      username: user?.username || '',
      location: user?.location || '',
      name: user?.name || '',
      phoneNumber: user?.phoneNumber || '',
      email: user?.email || '',
      idFront: user?.idFront || '',
      idBack: user?.idBack || '',
    });
    setEditMode(true);
  };

  const handleCancel = () => {
    setEditMode(false);
  };

  const handleSave = async () => {
    if (!editData.fullname.trim() || !editData.username.trim() || !editData.location.trim() || !editData.name.trim() || !editData.phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Full name, username, location, display name, and phone number are required fields.');
      return;
    }
    setSaving(true);
    try {
      const updatedUser = { ...user, ...editData };
      await userStorage.saveUser(updatedUser);
      setUser(updatedUser);
      setIsVerified(updatedUser.idFront && updatedUser.idBack);
      setEditMode(false);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (e) {
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    }
    setSaving(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            navigation.reset({
              index: 0,
              routes: [{ name: 'Auth' }],
            });
          }
        }
      ]
    );
  };

  // Image picker for ID upload
  const pickImage = async (side) => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to upload ID images.');
        return;
      }

      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setEditData({ ...editData, [side]: result.assets[0].uri });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#6366f1" />
        <LinearGradient
          colors={["#6366f1", "#8b5cf6"]}
          style={styles.loadingGradient}
        >
          <Feather name="user" size={64} color="#fff" />
          <Text style={styles.loadingText}>Loading your profile...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <StatusBar barStyle="light-content" backgroundColor="#6366f1" />

      {/* Gradient Header with Avatar */}
      <LinearGradient colors={["#6366f1", "#818cf8", "#f1f5f9"]} start={{x:0,y:0}} end={{x:1,y:1}} style={styles.headerGradient}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerIconBtn}>
            <Feather name="arrow-left" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>My Profile</Text>
          {!editMode ? (
            <TouchableOpacity onPress={handleEdit} style={styles.headerIconBtn}>
              <Feather name="edit-2" size={22} color="#fff" />
            </TouchableOpacity>
          ) : (
            <View style={styles.headerPlaceholder} />
          )}
        </View>

        {/* Avatar with verification badge */}
        <View style={styles.avatarContainer}>
          <View style={styles.avatarWrapper}>
            <Image source={{ uri: user.avatarUrl || AVATAR_PLACEHOLDER }} style={styles.avatar} />
            {isVerified && (
              <View style={styles.verificationBadge}>
                <Feather name="check" size={16} color="#fff" />
              </View>
            )}
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={{ paddingBottom: editMode ? 160 : 60 }}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
      >
        {/* Enhanced Profile Card */}
        <View style={styles.profileCard}>
          {editMode ? (
            <View style={styles.editForm}>
              <Text style={styles.editFormTitle}>Edit Profile Information</Text>
              <ProfileInput
                icon="account"
                placeholder="Full Name"
                value={editData.fullname}
                onChangeText={text => setEditData({ ...editData, fullname: text })}
                editable={!saving}
                required
              />
              <ProfileInput
                icon="at"
                placeholder="Username"
                value={editData.username}
                onChangeText={text => setEditData({ ...editData, username: text })}
                editable={!saving}
                required
              />
              <ProfileInput
                icon="map-marker"
                placeholder="Location"
                value={editData.location}
                onChangeText={text => setEditData({ ...editData, location: text })}
                editable={!saving}
                required
              />
              <ProfileInput
                icon="badge-account-horizontal"
                placeholder="Display Name"
                value={editData.name}
                onChangeText={text => setEditData({ ...editData, name: text })}
                editable={!saving}
                required
              />
              <ProfileInput
                icon="phone"
                placeholder="Phone Number"
                value={editData.phoneNumber}
                onChangeText={text => setEditData({ ...editData, phoneNumber: text })}
                keyboardType="phone-pad"
                editable={!saving}
                required
              />
              <ProfileInput
                icon="email"
                placeholder="Email (optional)"
                value={editData.email}
                onChangeText={text => setEditData({ ...editData, email: text })}
                keyboardType="email-address"
                editable={!saving}
              />
            </View>
          ) : (
            <View style={styles.profileInfo}>
              <Text style={styles.fullname}>{user.fullname || 'Not provided'}</Text>
              <Text style={styles.username}>@{user.username || 'username'}</Text>

              <View style={styles.infoSection}>
                <View style={styles.infoRow}>
                  <Feather name="map-pin" size={18} color="#6366f1" />
                  <Text style={styles.infoText}>{user.location || 'Location not set'}</Text>
                </View>

                <View style={styles.infoRow}>
                  <Feather name="phone" size={18} color="#6366f1" />
                  <Text style={styles.infoText}>{user.phoneNumber || 'Phone not set'}</Text>
                </View>

                {user.email && (
                  <View style={styles.infoRow}>
                    <Feather name="mail" size={18} color="#6366f1" />
                    <Text style={styles.infoText}>{user.email}</Text>
                  </View>
                )}
              </View>

              <View style={styles.badgeContainer}>
                <View style={styles.roleBadge}>
                  <Feather name="user" size={14} color="#6366f1" />
                  <Text style={styles.roleBadgeText}>
                    {user.role ? user.role.replace('/', ' / ') : 'Sender'}
                  </Text>
                </View>
                <View style={styles.idBadge}>
                  <Feather name="hash" size={14} color="#64748b" />
                  <Text style={styles.idBadgeText}>{user.userCode}</Text>
                </View>
                {isVerified && (
                  <View style={styles.verifiedBadge}>
                    <Feather name="shield-check" size={14} color="#10b981" />
                    <Text style={styles.verifiedBadgeText}>Verified</Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Enhanced Stats Section */}
          {!editMode && (
            <View style={styles.statsSection}>
              <Text style={styles.statsTitle}>Activity Overview</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statCard}>
                  <View style={styles.statIconContainer}>
                    <Feather name="send" size={24} color="#6366f1" />
                  </View>
                  <Text style={styles.statValue}>{stats.sent}</Text>
                  <Text style={styles.statLabel}>Packages Sent</Text>
                </View>

                <View style={styles.statCard}>
                  <View style={styles.statIconContainer}>
                    <Feather name="inbox" size={24} color="#10b981" />
                  </View>
                  <Text style={styles.statValue}>{stats.received}</Text>
                  <Text style={styles.statLabel}>Packages Received</Text>
                </View>

                <View style={[styles.statCard, styles.statCardWide]}>
                  <View style={styles.statIconContainer}>
                    <Feather name="dollar-sign" size={24} color="#f59e0b" />
                  </View>
                  <Text style={styles.statValue}>UGX {stats.totalValue.toLocaleString()}</Text>
                  <Text style={styles.statLabel}>Total Package Value</Text>
                </View>
              </View>
            </View>
          )}
        </View>

        {/* Enhanced ID Verification Section */}
        <View style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleRow}>
              <Feather name="shield" size={20} color="#6366f1" />
              <Text style={styles.sectionTitle}>Identity Verification</Text>
            </View>
            {isVerified && (
              <View style={styles.verificationStatus}>
                <Feather name="check-circle" size={16} color="#10b981" />
                <Text style={styles.verificationStatusText}>Verified</Text>
              </View>
            )}
          </View>

          <Text style={styles.sectionDescription}>
            Upload clear photos of your government-issued ID for account verification
          </Text>

          <View style={styles.idCardsContainer}>
            <View style={styles.idCardWrapper}>
              <TouchableOpacity
                disabled={!editMode || saving}
                onPress={() => pickImage('idFront')}
                style={[styles.idCardTouchable, editMode && styles.idCardEditable]}
                activeOpacity={0.8}
              >
                <Image
                  source={{ uri: (editMode ? editData.idFront : user.idFront) || ID_PLACEHOLDER }}
                  style={styles.idImage}
                />
                <View style={styles.idOverlay}>
                  <Text style={styles.idLabel}>ID Front</Text>
                  {editMode && (
                    <View style={styles.uploadIndicator}>
                      <Feather name="camera" size={16} color="#6366f1" />
                      <Text style={styles.uploadText}>Tap to upload</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.idCardWrapper}>
              <TouchableOpacity
                disabled={!editMode || saving}
                onPress={() => pickImage('idBack')}
                style={[styles.idCardTouchable, editMode && styles.idCardEditable]}
                activeOpacity={0.8}
              >
                <Image
                  source={{ uri: (editMode ? editData.idBack : user.idBack) || ID_PLACEHOLDER }}
                  style={styles.idImage}
                />
                <View style={styles.idOverlay}>
                  <Text style={styles.idLabel}>ID Back</Text>
                  {editMode && (
                    <View style={styles.uploadIndicator}>
                      <Feather name="camera" size={16} color="#6366f1" />
                      <Text style={styles.uploadText}>Tap to upload</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Enhanced Actions Section */}
        {!editMode && (
          <View style={styles.actionsSection}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('RoleSelector')}
              activeOpacity={0.8}
            >
              <View style={styles.actionIconContainer}>
                <Feather name="repeat" size={20} color="#f59e0b" />
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Switch Role</Text>
                <Text style={styles.actionSubtitle}>Change to Traveller or Agent mode</Text>
              </View>
              <Feather name="chevron-right" size={20} color="#94a3b8" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {/* Navigate to settings */}}
              activeOpacity={0.8}
            >
              <View style={styles.actionIconContainer}>
                <Feather name="settings" size={20} color="#6366f1" />
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Settings</Text>
                <Text style={styles.actionSubtitle}>App preferences and notifications</Text>
              </View>
              <Feather name="chevron-right" size={20} color="#94a3b8" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {/* Navigate to help */}}
              activeOpacity={0.8}
            >
              <View style={styles.actionIconContainer}>
                <Feather name="help-circle" size={20} color="#10b981" />
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Help & Support</Text>
                <Text style={styles.actionSubtitle}>Get help and contact support</Text>
              </View>
              <Feather name="chevron-right" size={20} color="#94a3b8" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.logoutButton]}
              onPress={handleLogout}
              activeOpacity={0.8}
            >
              <View style={[styles.actionIconContainer, styles.logoutIconContainer]}>
                <Feather name="log-out" size={20} color="#ef4444" />
              </View>
              <View style={styles.actionContent}>
                <Text style={[styles.actionTitle, styles.logoutTitle]}>Logout</Text>
                <Text style={styles.actionSubtitle}>Sign out of your account</Text>
              </View>
              <Feather name="chevron-right" size={20} color="#94a3b8" />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Enhanced Edit Mode Action Bar */}
      {editMode && (
        <View style={styles.editActionBar}>
          <TouchableOpacity
            onPress={handleCancel}
            style={styles.cancelButton}
            disabled={saving}
          >
            <Feather name="x" size={18} color="#64748b" />
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleSave}
            style={[styles.saveButton, saving && styles.saveButtonDisabled]}
            disabled={saving}
          >
            {saving ? (
              <>
                <Text style={styles.saveButtonText}>Saving...</Text>
              </>
            ) : (
              <>
                <Feather name="check" size={18} color="#fff" />
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

// Enhanced ProfileInput component
const ProfileInput = ({ icon, required, ...props }) => (
  <View style={styles.inputContainer}>
    <View style={styles.inputRow}>
      <MaterialCommunityIcons name={icon} size={20} color="#6366f1" />
      <TextInput
        style={styles.input}
        placeholderTextColor="#94a3b8"
        {...props}
      />
      {required && <Text style={styles.requiredIndicator}>*</Text>}
    </View>
  </View>
);

const styles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: '#f8fafc'
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f8fafc'
  },
  loadingGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 16,
    fontWeight: '600'
  },

  // Header styles
  headerGradient: {
    height: 200,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
    position: 'relative',
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'web' ? 40 : 20,
    marginBottom: 20,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: 0.5,
    flex: 1,
    textAlign: 'center'
  },
  headerIconBtn: {
    padding: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerPlaceholder: {
    width: 44,
    height: 44,
  },

  // Avatar styles
  avatarContainer: {
    alignItems: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: -60
  },
  avatarWrapper: {
    position: 'relative',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 6,
    borderColor: '#fff',
    backgroundColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  verificationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: '#10b981',
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },

  // Scroll view styles
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },

  // Profile card styles
  profileCard: {
    backgroundColor: '#fff',
    borderRadius: 24,
    marginTop: 80,
    marginHorizontal: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },

  // Profile info styles
  profileInfo: {
    alignItems: 'center',
  },
  fullname: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  username: {
    fontSize: 16,
    color: '#6366f1',
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '600',
  },

  infoSection: {
    width: '100%',
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 16,
    color: '#334155',
    marginLeft: 12,
    fontWeight: '500',
  },

  // Badge styles
  badgeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 20,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e0e7ff',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  roleBadgeText: {
    color: '#6366f1',
    fontWeight: '600',
    fontSize: 12,
  },
  idBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  idBadgeText: {
    color: '#64748b',
    fontWeight: '600',
    fontSize: 12,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#dcfce7',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  verifiedBadgeText: {
    color: '#10b981',
    fontWeight: '600',
    fontSize: 12,
  },

  // Edit form styles
  editForm: {
    alignItems: 'stretch',
  },
  editFormTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 24,
  },

  // Stats styles
  statsSection: {
    marginTop: 24,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    minWidth: '30%',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  statCardWide: {
    flex: 1,
    minWidth: '100%',
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    fontWeight: '500',
  },

  // Section card styles
  sectionCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1e293b',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 20,
    lineHeight: 20,
  },
  verificationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  verificationStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#10b981',
  },

  // ID verification styles
  idCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 16,
  },
  idCardWrapper: {
    flex: 1,
  },
  idCardTouchable: {
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  idCardEditable: {
    borderWidth: 2,
    borderColor: '#6366f1',
    borderStyle: 'dashed',
  },
  idImage: {
    width: '100%',
    height: 120,
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
  },
  idOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 8,
    alignItems: 'center',
  },
  idLabel: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  uploadIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  uploadText: {
    fontSize: 10,
    color: '#6366f1',
    fontWeight: '500',
  },

  // Actions section styles
  actionsSection: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  logoutButton: {
    borderColor: '#fee2e2',
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  logoutIconContainer: {
    backgroundColor: '#fee2e2',
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  logoutTitle: {
    color: '#ef4444',
  },
  actionSubtitle: {
    fontSize: 13,
    color: '#64748b',
  },

  // Edit action bar styles
  editActionBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    gap: 8,
    flex: 1,
    marginRight: 10,
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: '#64748b',
    fontWeight: '600',
    fontSize: 16,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6366f1',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 24,
    gap: 8,
    flex: 2,
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },

  // Input styles
  inputContainer: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    gap: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1e293b',
    paddingVertical: 12,
    fontWeight: '500',
  },
  requiredIndicator: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileScreen;