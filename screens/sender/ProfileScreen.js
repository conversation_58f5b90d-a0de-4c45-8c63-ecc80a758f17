import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Platform, TextInput, Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import Feather from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userStorage, userPackageStorage } from '../../utils/storage';

const AVATAR_PLACEHOLDER = 'https://ui-avatars.com/api/?name=User&background=6366f1&color=fff&size=128';
const ID_PLACEHOLDER = 'https://cdn.jsdelivr.net/gh/ghkatende/cdn/id-placeholder.png';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({ sent: 0, received: 0 });
  const [editMode, setEditMode] = useState(false);
  const [editData, setEditData] = useState({ fullname: '', username: '', location: '', name: '', phoneNumber: '', email: '', idFront: '', idBack: '' });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      const u = await userStorage.getUser();
      setUser(u);
      if (u) {
        const sent = (await userPackageStorage.getPackages(u.userCode, u.role)).filter(pkg => pkg.senderId === u.userCode).length;
        const received = (await userPackageStorage.getPackages(u.userCode, u.role)).filter(pkg => pkg.receiverId === u.userCode).length;
        setStats({ sent, received });
        setEditData({
          fullname: u.fullname || '',
          username: u.username || '',
          location: u.location || '',
          name: u.name || '',
          phoneNumber: u.phoneNumber || '',
          email: u.email || '',
          idFront: u.idFront || '',
          idBack: u.idBack || '',
        });
      }
    };
    loadUser();
  }, []);

  const handleEdit = () => {
    setEditData({
      fullname: user?.fullname || '',
      username: user?.username || '',
      location: user?.location || '',
      name: user?.name || '',
      phoneNumber: user?.phoneNumber || '',
      email: user?.email || '',
      idFront: user?.idFront || '',
      idBack: user?.idBack || '',
    });
    setEditMode(true);
  };

  const handleCancel = () => {
    setEditMode(false);
  };

  const handleSave = async () => {
    if (!editData.fullname.trim() || !editData.username.trim() || !editData.location.trim() || !editData.name.trim() || !editData.phoneNumber.trim()) {
      Alert.alert('Validation', 'Fullname, Username, Location, Name, and Phone number are required.');
      return;
    }
    setSaving(true);
    try {
      const updatedUser = { ...user, ...editData };
      await userStorage.setUser(updatedUser);
      setUser(updatedUser);
      setEditMode(false);
    } catch (e) {
      Alert.alert('Error', 'Failed to save profile.');
    }
    setSaving(false);
  };

  // Image picker for ID upload
  const pickImage = async (side) => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.7,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setEditData({ ...editData, [side]: result.assets[0].uri });
    }
  };

  if (!user) {
    return (
      <View className="flex-1 justify-center items-center bg-slate-50">
        <Feather name="user" size={48} color="#6366f1" />
        <Text className="text-lg text-slate-400 mt-4">Loading profile...</Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-slate-50 px-4 pt-2" contentContainerStyle={{ paddingBottom: 40 }}>
      {/* Custom Header with Back and Edit */}
      <View style={{ flexDirection: 'row', alignItems: 'center', paddingTop: Platform.OS === 'web' ? 32 : 16, paddingBottom: 12, backgroundColor: 'white', marginHorizontal: -16, marginBottom: 8 }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginLeft: 8, marginRight: 12, padding: 6 }}>
          <Feather name="arrow-left" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#1e293b', flex: 1 }}>Profile</Text>
        {!editMode && (
          <TouchableOpacity onPress={handleEdit} style={{ marginRight: 16, padding: 6 }}>
            <Feather name="edit-2" size={22} color="#6366f1" />
          </TouchableOpacity>
        )}
      </View>

      {/* Profile Content */}
      <View className="items-center mb-8">
        <Image
          source={{ uri: user.avatarUrl || AVATAR_PLACEHOLDER }}
          style={{ width: 96, height: 96, borderRadius: 48, marginBottom: 12, borderWidth: 3, borderColor: '#6366f1', backgroundColor: '#e0e7ef' }}
        />
        {editMode ? (
          <>
            <TextInput
              value={editData.fullname}
              onChangeText={text => setEditData({ ...editData, fullname: text })}
              placeholder="Full Name"
              style={{ fontSize: 18, color: '#1e293b', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              editable={!saving}
            />
            <TextInput
              value={editData.username}
              onChangeText={text => setEditData({ ...editData, username: text })}
              placeholder="Username"
              style={{ fontSize: 16, color: '#334155', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              editable={!saving}
            />
            <TextInput
              value={editData.location}
              onChangeText={text => setEditData({ ...editData, location: text })}
              placeholder="Location"
              style={{ fontSize: 16, color: '#334155', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              editable={!saving}
            />
            <TextInput
              value={editData.name}
              onChangeText={text => setEditData({ ...editData, name: text })}
              placeholder="Display Name"
              style={{ fontSize: 16, color: '#334155', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              editable={!saving}
            />
            <TextInput
              value={editData.phoneNumber}
              onChangeText={text => setEditData({ ...editData, phoneNumber: text })}
              placeholder="Phone Number"
              style={{ fontSize: 16, color: '#334155', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              keyboardType="phone-pad"
              editable={!saving}
            />
            <TextInput
              value={editData.email}
              onChangeText={text => setEditData({ ...editData, email: text })}
              placeholder="Email (optional)"
              style={{ fontSize: 16, color: '#334155', marginBottom: 6, textAlign: 'center', borderBottomWidth: 1, borderColor: '#e5e7eb', width: 220 }}
              keyboardType="email-address"
              editable={!saving}
            />
            {/* ID Uploads */}
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 10 }}>
              <TouchableOpacity onPress={() => pickImage('idFront')} disabled={saving} style={{ alignItems: 'center', marginRight: 16 }}>
                <Image source={{ uri: editData.idFront || ID_PLACEHOLDER }} style={{ width: 70, height: 50, borderRadius: 8, borderWidth: 1, borderColor: '#cbd5e1', marginBottom: 4 }} />
                <Text style={{ fontSize: 12, color: '#6366f1' }}>Upload ID Front</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => pickImage('idBack')} disabled={saving} style={{ alignItems: 'center' }}>
                <Image source={{ uri: editData.idBack || ID_PLACEHOLDER }} style={{ width: 70, height: 50, borderRadius: 8, borderWidth: 1, borderColor: '#cbd5e1', marginBottom: 4 }} />
                <Text style={{ fontSize: 12, color: '#6366f1' }}>Upload ID Back</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <>
            <Text className="text-xl font-bold text-slate-900 mb-1">{user.fullname || '-'}</Text>
            <Text className="text-base text-slate-500 mb-1">@{user.username || '-'}</Text>
            <Text className="text-base text-slate-400 mb-1">{user.location || '-'}</Text>
            <Text className="text-lg font-semibold text-slate-900 mb-1">{user.name || 'User'}</Text>
            <View className="flex-row items-center mb-2">
              <Text className="text-base text-slate-500 mr-2">{user.phoneNumber}</Text>
              {user.email && <Text className="text-base text-slate-400">• {user.email}</Text>}
            </View>
            {/* ID Images */}
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 10 }}>
              <View style={{ alignItems: 'center', marginRight: 16 }}>
                <Image source={{ uri: user.idFront || ID_PLACEHOLDER }} style={{ width: 70, height: 50, borderRadius: 8, borderWidth: 1, borderColor: '#cbd5e1', marginBottom: 4 }} />
                <Text style={{ fontSize: 12, color: '#64748b' }}>ID Front</Text>
              </View>
              <View style={{ alignItems: 'center' }}>
                <Image source={{ uri: user.idBack || ID_PLACEHOLDER }} style={{ width: 70, height: 50, borderRadius: 8, borderWidth: 1, borderColor: '#cbd5e1', marginBottom: 4 }} />
                <Text style={{ fontSize: 12, color: '#64748b' }}>ID Back</Text>
              </View>
            </View>
          </>
        )}
        <View className="flex-row items-center mt-1">
          <Text className="text-xs bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full font-semibold mr-2">{user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'Sender'}</Text>
          <Text className="text-xs bg-slate-100 text-slate-500 px-3 py-1 rounded-full font-semibold">ID: {user.userCode}</Text>
        </View>
      </View>

      {/* Edit Mode Action Buttons */}
      {editMode && (
        <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 18 }}>
          <TouchableOpacity
            onPress={handleSave}
            style={{ backgroundColor: '#6366f1', paddingVertical: 10, paddingHorizontal: 28, borderRadius: 10, marginRight: 10, opacity: saving ? 0.7 : 1 }}
            disabled={saving}
          >
            <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            style={{ backgroundColor: '#e5e7eb', paddingVertical: 10, paddingHorizontal: 22, borderRadius: 10 }}
            disabled={saving}
          >
            <Text style={{ color: '#334155', fontWeight: 'bold', fontSize: 16 }}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Stats */}
      <View className="flex-row justify-center mb-8">
        <View className="items-center mx-6">
          <Text className="text-2xl font-extrabold text-indigo-600">{stats.sent}</Text>
          <Text className="text-xs text-slate-500 font-semibold mt-1">Sent</Text>
        </View>
        <View className="items-center mx-6">
          <Text className="text-2xl font-extrabold text-green-600">{stats.received}</Text>
          <Text className="text-xs text-slate-500 font-semibold mt-1">Received</Text>
        </View>
      </View>

      {/* Switch to Traveller Mode */}
      <TouchableOpacity
        className="flex-row items-center justify-center bg-yellow-50 border border-yellow-300 rounded-xl py-4 mb-8"
        onPress={() => navigation.navigate('RoleSelector')}
        activeOpacity={0.85}
        disabled={editMode}
      >
        <Feather name="repeat" size={20} color="#eab308" />
        <Text className="text-yellow-700 font-bold text-base ml-2">Switch to Traveller Mode</Text>
      </TouchableOpacity>

      {/* Logout Button */}
      <TouchableOpacity
        className="flex-row items-center justify-center bg-red-50 border border-red-200 rounded-xl py-4"
        onPress={() => navigation.navigate('Logout')}
        activeOpacity={0.85}
        disabled={editMode}
      >
        <Feather name="log-out" size={20} color="#ef4444" />
        <Text className="text-red-600 font-bold text-base ml-2">Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default ProfileScreen; 