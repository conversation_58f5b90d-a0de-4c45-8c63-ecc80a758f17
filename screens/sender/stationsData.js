export const stations = [
  {
    id: 'STN-KLA-001',
    name: 'Total Kampala Central',
    image: { uri: 'https://placehold.co/128x128?text=Station+1' },
    hours: '24hrs',
    distance: 1.2,
    openNow: true,
    address: 'Kampala Central, Uganda',
    phone: '+256 123 456 789',
    coordinates: { lat: 0.3476, lng: 32.5825 },
  },
  {
    id: 'STN-KLA-002',
    name: 'Shell Lugogo',
    image: { uri: 'https://placehold.co/128x128?text=Station+2' },
    hours: '6am–10pm',
    distance: 2.5,
    openNow: false,
    address: 'Lugogo, Kampala, Uganda',
    phone: '+256 123 456 790',
    coordinates: { lat: 0.3206, lng: 32.5850 },
  },
  {
    id: 'STN-KLA-003',
    name: 'City Oil Nakawa',
    image: { uri: 'https://placehold.co/128x128?text=Station+3' },
    hours: '7am–9pm',
    distance: 3.1,
    openNow: true,
    address: 'Nakawa, Kampala, Uganda',
    phone: '+256 123 456 791',
    coordinates: { lat: 0.3186, lng: 32.6086 },
  },
  {
    id: 'STN-KLA-004',
    name: 'Total Ntinda',
    image: { uri: 'https://placehold.co/128x128?text=Station+4' },
    hours: '5am–11pm',
    distance: 4.0,
    openNow: true,
    address: 'Ntinda, Kampala, Uganda',
    phone: '+256 123 456 792',
    coordinates: { lat: 0.3347, lng: 32.6186 },
  },
  {
    id: 'STN-KLA-005',
    name: 'Shell Bugolobi',
    image: { uri: 'https://placehold.co/128x128?text=Station+5' },
    hours: '24hrs',
    distance: 2.8,
    openNow: false,
    address: 'Bugolobi, Kampala, Uganda',
    phone: '+256 123 456 793',
    coordinates: { lat: 0.3086, lng: 32.6186 },
  },
  {
    id: 'STN-KLA-006',
    name: 'City Oil Bwaise',
    image: { uri: 'https://placehold.co/128x128?text=Station+6' },
    hours: '6am–10pm',
    distance: 5.2,
    openNow: true,
    address: 'Bwaise, Kampala, Uganda',
    phone: '+256 123 456 794',
    coordinates: { lat: 0.3476, lng: 32.5825 },
  },
  ...Array.from({ length: 94 }, (_, i) => {
    const n = i + 7;
    return {
      id: `STN-KLA-${n.toString().padStart(3, '0')}`,
      name: `Station ${n}`,
      image: { uri: `https://placehold.co/128x128?text=Station+${n}` },
      hours: n % 3 === 0 ? '24hrs' : n % 3 === 1 ? '6am–10pm' : '7am–9pm',
      distance: (1 + (n % 10) * 0.7).toFixed(1),
      openNow: n % 4 !== 0,
      address: `Location ${n}, Kampala, Uganda`,
      phone: `+256 123 456 ${(790 + n).toString().padStart(3, '0')}`,
      coordinates: { 
        lat: 0.3476 + (n * 0.001), 
        lng: 32.5825 + (n * 0.001) 
      },
    };
  })
]; 