import React from 'react';
import { View, Text } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';

/**
 * DeliveryMapView
 * Props:
 * - station: { name, coordinates: { lat, lng } }
 * - userLocation: { lat, lng } (optional, for future use)
 * - children: ReactNode (floating overlays/cards)
 */
const DeliveryMapView = ({ station, userLocation, children }) => {
  return (
    <View className="flex-1 bg-slate-200 relative">
      {/* Map Background */}
      <View className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100 opacity-50" />

      {/* Station Marker - moved up to 30% from top */}
      <View className="absolute left-1/2" style={{ top: '30%', transform: [{ translateX: -18 }] }}>
        <View className="bg-red-500 w-6 h-6 rounded-full border-2 border-white shadow-lg items-center justify-center">
          <Feather name="map-pin" size={16} color="white" />
        </View>
        <View className="bg-white px-2 py-1 rounded-lg shadow-md mt-2">
          <Text className="text-xs font-semibold text-slate-800">{station?.name || 'Station'}</Text>
        </View>
      </View>

      {/* Route Line (mocked) - from user to station */}
      <View className="absolute left-1/2" style={{ top: '15%', width: 2, height: '15%', backgroundColor: '#3b82f6', opacity: 0.5, zIndex: 1, transform: [{ translateX: -1 }] }} />

      {/* User Location (mocked) - moved up to 15% from top */}
      <View className="absolute left-1/2" style={{ top: '15%', transform: [{ translateX: -8 }] }}>
        <View className="bg-blue-500 w-4 h-4 rounded-full border-2 border-white shadow-lg" />
      </View>

      {/* Floating overlays/cards */}
      {children}
    </View>
  );
};

export default DeliveryMapView; 