import React from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';
import { LinearGradient } from 'expo-linear-gradient';

function getGreeting() {
  const hour = new Date().getHours();
  if (hour < 12) return 'Good morning';
  if (hour < 18) return 'Good afternoon';
  return 'Good evening';
}

const DashboardScreen = () => {
  const name = '<PERSON>'; // Placeholder
  const trancpotaId = 'TRN-123456'; // Placeholder
  const avatarUrl = null; // Placeholder, use null for initials
  const tagline = 'Cheap to send, quick to receive.';
  const packages = [
    { id: 'PKG-UGA-123456', status: 'In Transit', eta: '2h 15m', to: 'Kampala', traveler: 'Jane', station: 'STN-KLA-001' },
    { id: 'PKG-UGA-654321', status: 'At Station', eta: 'Ready for pickup', to: 'Entebbe', traveler: '<PERSON>', station: 'STN-EBB-002' },
  ];
  const alerts = [
    { id: 1, text: 'Storage fee applied to PKG-UGA-123456', icon: 'alert-circle', color: 'text-yellow-500' },
    { id: 2, text: 'Rate your last traveler', icon: 'star', color: 'text-indigo-500' },
  ];

  return (
    <ScrollView className="flex-1 bg-slate-50 px-4 pt-8" contentContainerStyle={{ paddingBottom: 40 }}>
      {/* Greeting Card with Gradient */}
      <LinearGradient
        colors={["#e0e7ff", "#f1f5f9"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="rounded-3xl shadow-lg px-6 py-6 mb-7 w-full max-w-xl mx-auto"
        style={{ minHeight: 120 }}
      >
        <View className="flex-row items-center mb-2">
          {avatarUrl ? (
            <Image source={{ uri: avatarUrl }} className="w-12 h-12 rounded-full mr-3" />
          ) : (
            <View className="w-12 h-12 rounded-full bg-indigo-200 items-center justify-center mr-3">
              <Text className="text-xl font-bold text-indigo-700">{name[0]}</Text>
            </View>
          )}
          <View>
            <Text className="text-lg font-bold text-slate-900 leading-tight">{getGreeting()}, {name}</Text>
            <View className="flex-row items-center mt-0.5">
              <Text className="text-xs text-slate-400 font-semibold mr-2">ID: {trancpotaId}</Text>
              <TouchableOpacity onPress={() => {}} className="p-1">
                <Feather name="copy" size={16} color="#a5b4fc" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <Text className="text-base text-slate-500 font-medium mb-1 ml-1">{tagline}</Text>
      </LinearGradient>

      {/* Quick Actions - visually separated */}
      <View className="flex-row justify-between w-full max-w-xl mx-auto mb-8 space-x-5">
        <TouchableOpacity className="flex-1 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl py-5 items-center shadow-lg border border-green-100" onPress={() => {}} activeOpacity={0.88}>
          <Feather name="plus-circle" size={28} color="#fff" className="mb-2" />
          <Text className="text-white font-bold text-base tracking-wide">Send Package</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-1 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl py-5 items-center shadow-lg border border-blue-100" onPress={() => {}} activeOpacity={0.88}>
          <Feather name="search" size={28} color="#fff" className="mb-2" />
          <Text className="text-white font-bold text-base tracking-wide">Track Package</Text>
        </TouchableOpacity>
      </View>

      {/* Divider */}
      <View className="w-full max-w-xl mx-auto mb-5">
        <View className="h-0.5 bg-slate-200 rounded-full" />
      </View>

      {/* Package Status Cards */}
      <View className="w-full max-w-xl mx-auto mb-7">
        <View className="flex-row justify-between items-center mb-2">
          <Text className="text-lg font-bold text-slate-900">My Packages</Text>
          <TouchableOpacity onPress={() => {}}>
            <Text className="text-indigo-500 font-semibold text-sm">See all</Text>
          </TouchableOpacity>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="flex-row space-x-4">
          {packages.slice(0, 2).map(pkg => (
            <View key={pkg.id} className="bg-white rounded-2xl shadow-lg px-6 py-5 mr-3 min-w-[220px] max-w-[240px] border border-slate-100">
              <View className="flex-row items-center mb-1">
                <Feather name="package" size={20} color="#6366f1" className="mr-2" />
                <Text className="text-xs font-bold text-slate-400">{pkg.status}</Text>
              </View>
              <Text className="text-base font-semibold text-slate-900 mb-1">To: {pkg.to}</Text>
              <Text className="text-xs text-slate-500 mb-0.5">Traveler: {pkg.traveler}</Text>
              <Text className="text-xs text-slate-500 mb-0.5">Station: {pkg.station}</Text>
              <Text className="text-xs text-slate-400 mt-1">ETA: {pkg.eta}</Text>
              <Text className="text-xs text-slate-300 mt-1">{pkg.id}</Text>
            </View>
          ))}
        </ScrollView>
      </View>

      {/* Alerts / Rating Prompts */}
      {alerts.length > 0 && (
        <View className="w-full max-w-xl mx-auto mb-2">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-lg font-bold text-slate-900">Alerts</Text>
          </View>
          {alerts.map(alert => (
            <View key={alert.id} className="flex-row items-center bg-white rounded-2xl shadow-lg px-5 py-4 mb-3 border border-slate-100">
              <Feather name={alert.icon} size={22} className={`mr-3 ${alert.color}`} />
              <Text className="text-slate-700 flex-1 text-sm font-medium">{alert.text}</Text>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

export default DashboardScreen; 