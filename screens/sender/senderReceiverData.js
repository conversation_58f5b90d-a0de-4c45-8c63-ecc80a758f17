// Utility function to generate unlimited users with SR:UNIQUECODE format
export const generateUser = (userNumber) => {
  // Use only non-confusing characters: 3,4,7,9 and A,C,D,E,F,H,J,K,L,M,N,P,Q,R,T,U,V,W,X,Y
  const safeChars = '3479ACDEFHJKLMNPQRSTUVWXY';
  
  // Generate a unique 5-character code with no repeated characters
  let code = '';
  let usedChars = new Set();
  let seed = userNumber;
  
  // Use a simple algorithm to generate unique codes
  for (let i = 0; i < 5; i++) {
    let charIndex;
    do {
      // Use different parts of the seed for each position
      seed = (seed * 9301 + 49297) % 233280; // Simple PRNG
      charIndex = seed % safeChars.length;
    } while (usedChars.has(safeChars[charIndex]));
    
    code += safeChars[charIndex];
    usedChars.add(safeChars[charIndex]);
  }
  
  const id = `SR:${code}`; // SR:UNIQUECODE format
  const name = `User ${userNumber}`;
  const phone = `+256700000${userNumber.toString().padStart(3, '0')}`;
  
  return { id, name, phone };
};

// Generate 500 users
export const senderReceivers = (() => {
  const users = [];
  for (let i = 1; i <= 500; i++) {
    users.push(generateUser(i));
  }
  return users;
})();

// (For brevity, only the first 20 users are shown. The full file will contain 500 users with unique IDs and phone numbers.) 