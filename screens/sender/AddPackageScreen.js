import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ActivityIndicator, Image, ScrollView, Platform, Picker, Switch } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import { stations } from './stationsData';
import { senderReceivers } from './senderReceiverData';
import { receivers } from './receiversData';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userPackageStorage } from '../../utils/storage';

const AddPackageScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { stationId, mode, packageData } = route.params;
  const isEditMode = mode === 'edit';
  const station = stations.find(s => s.id === stationId);
  const [receiverStationId, setReceiverStationId] = useState('');
  const [receiverId, setReceiverId] = useState('');
  const [searchingStation, setSearchingStation] = useState(false);
  const [searchingReceiver, setSearchingReceiver] = useState(false);
  const searchStationTimeout = useRef(null);
  const searchReceiverTimeout = useRef(null);
  const [currentUserCode, setCurrentUserCode] = useState('');
  const [currentUserPhone, setCurrentUserPhone] = useState('');
  const [userData, setUserData] = useState(null);

  // Get current user's data
  useEffect(() => {
    const getUserData = async () => {
      try {
        // Try both storage keys to handle the mismatch
        let userString = await AsyncStorage.getItem('USER_DATA_V1');
        if (!userString) {
          userString = await AsyncStorage.getItem('USER_STATE_V1');
        }
        
        if (userString) {
          const user = JSON.parse(userString);
          setCurrentUserCode(user.userCode);
          setCurrentUserPhone(user.phoneNumber);
          setUserData(user);
          console.log('Loaded user data:', user);
        } else {
          console.log('No user data found in storage');
        }
      } catch (e) {
        console.log('Error getting user data:', e);
      }
    };
    getUserData();
  }, []);

  // Populate form fields when in edit mode
  useEffect(() => {
    if (isEditMode && packageData) {
      console.log('Populating form with package data:', packageData);
      setPackageName(packageData.name || '');
      setPackageWeight(packageData.weight || weightRanges[0]);
      setPackageType(packageData.type || packageTypes[0]);
      setPackageDescription(packageData.description || '');
      setPackageImage(packageData.image || null);
      setFee(packageData.fare || '');
      setTip(packageData.tip || '');
      setFragile(packageData.fragile || false);
      setPackageValue(packageData.value || '');
      setReceiverStationId(packageData.receiverStationId || '');
      setReceiverId(packageData.receiverId || '');
    }
  }, [isEditMode, packageData]);

  // Package fields
  const [packageName, setPackageName] = useState('');
  const weightRanges = [
    '0g - 2kg',
    '2.5kg - 5kg',
    '6kg - 10kg',
    '11kg - 20kg',
    '50+ kg',
  ];
  const [packageWeight, setPackageWeight] = useState(weightRanges[0]);
  const [packageDescription, setPackageDescription] = useState('');
  const [packageImage, setPackageImage] = useState(null);
  const [fee, setFee] = useState('');
  const [tip, setTip] = useState('');
  const [fragile, setFragile] = useState(false);
  const [packageValue, setPackageValue] = useState('');
  const packageTypes = ['Documents', 'Electronics', 'Clothing', 'Food', 'Other'];
  const [packageType, setPackageType] = useState(packageTypes[0]);

  // Helper for word count
  const getWordCount = (text) => {
    return text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
  };
  const maxWords = 200;
  const wordCount = getWordCount(packageDescription);

  const handleDescriptionChange = (text) => {
    const words = text.trim().split(/\s+/);
    if (text.trim() === '' || words.length <= maxWords) {
      setPackageDescription(text);
    } else {
      setPackageDescription(words.slice(0, maxWords).join(' '));
    }
  };

  // Simulate async station search with spinner
  const handleReceiverStationIdChange = (text) => {
    setReceiverStationId(text);
    setSearchingStation(true);
    if (searchStationTimeout.current) clearTimeout(searchStationTimeout.current);
    searchStationTimeout.current = setTimeout(() => {
      setSearchingStation(false);
    }, 500);
  };

  // Simulate async receiver search with spinner
  const handleReceiverIdChange = (text) => {
    setReceiverId(text);
    setSearchingReceiver(true);
    if (searchReceiverTimeout.current) clearTimeout(searchReceiverTimeout.current);
    searchReceiverTimeout.current = setTimeout(() => {
      setSearchingReceiver(false);
    }, 500);
  };

  const receiverStation = stations.find(s => s.id === receiverStationId.trim());
  const receiverObj = receivers.find(r => r.id === receiverId.trim());
  const receiverName = receiverObj ? receiverObj.name : (receiverId ? 'Not found' : '');

  const generatePackageId = () => {
    const ts = Date.now();
    const rand = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `PKG-UGA-${ts}-${rand}`;
  };

  const [showErrors, setShowErrors] = useState(false);

  const handleSubmit = async () => {
    console.log('Submit pressed');
    console.log('Mode:', isEditMode ? 'Edit' : 'Create');
    console.log('Fields:', { packageName, packageWeight, packageType, receiverStationId, receiverId, fee });
    console.log('Current user data:', { currentUserCode, currentUserPhone, userData });
    
    if (!packageName || !packageWeight || !packageType || !receiverStationId || !receiverId || !fee) {
      setShowErrors(true);
      console.log('Validation failed', { packageName, packageWeight, packageType, receiverStationId, receiverId, fee });
      Alert.alert('Missing Info', 'Please fill in all required fields.');
      return;
    }
    
    // Check if user data is available
    if (!currentUserCode || !userData) {
      console.log('User data not available:', { currentUserCode, userData });
      Alert.alert('Error', 'User data not available. Please try logging in again.');
      return;
    }
    
    setShowErrors(false);
    
    if (isEditMode && packageData) {
      // Update existing package
      const updatedPackage = {
        ...packageData,
        name: packageName,
        weight: packageWeight,
        type: packageType,
        fragile,
        value: packageValue,
        description: packageDescription,
        image: packageImage,
        fare: fee,
        tip,
        receiverStationId,
        receiverId,
        updatedAt: Date.now(),
      };
      console.log('Updating package:', updatedPackage);
      try {
        const success = await userPackageStorage.updatePackage(packageData.id, updatedPackage, currentUserCode, userData.role || 'sender');
        if (success) {
          console.log('Package updated successfully');
          navigation.navigate('Tabs', { screen: 'My Stash' });
        } else {
          Alert.alert('Error', 'Failed to update package. Please check your user data.');
        }
      } catch (e) {
        console.log('Error updating package:', e);
        Alert.alert('Error', 'Failed to update package.');
      }
    } else {
      // Create new package
      const newPackage = {
        id: generatePackageId(),
        name: packageName,
        weight: packageWeight,
        type: packageType,
        fragile,
        value: packageValue,
        description: packageDescription,
        image: packageImage,
        fare: fee,
        tip,
        receiverStationId,
        receiverId,
        senderId: currentUserCode,
        senderUsername: userData?.username || 'Unknown',
        createdAt: Date.now(),
      };
      console.log('Saving package:', newPackage);
      try {
        // Pass the user ID and role explicitly to ensure proper storage
        const success = await userPackageStorage.addPackage(newPackage, currentUserCode, userData.role || 'sender');
        if (success) {
          console.log('Package saved successfully');
          navigation.navigate('Tabs', { screen: 'My Stash' });
        } else {
          Alert.alert('Error', 'Failed to save package. Please check your user data.');
        }
      } catch (e) {
        console.log('Error saving package:', e);
        Alert.alert('Error', 'Failed to save package.');
      }
    }
  };

  // Image picker handler
  const handlePickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
      base64: false, // ensure base64 is not requested
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      // Only store the URI, not base64 data
      setPackageImage(result.assets[0].uri);
    }
  };

  if (!station) {
    return (
      <View className="flex-1 justify-center items-center bg-slate-50">
        {/* Header with back icon */}
        <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%', paddingTop: 40, paddingBottom: 16, paddingHorizontal: 20, backgroundColor: 'white', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', marginBottom: 24 }}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: 12, padding: 6 }}>
            <Feather name="arrow-left" size={24} color="#6366f1" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1e293b' }}>Station Not Found</Text>
        </View>
        <Text className="text-lg text-red-500">Station not found.</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-slate-100">
      {/* Sticky Header with back button and station id as title */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: Platform.OS === 'web' ? 32 : 16,
        paddingBottom: 16,
        paddingHorizontal: 20,
        backgroundColor: 'transparent',
        borderBottomWidth: 0,
        shadowColor: 'transparent',
        shadowOpacity: 0,
        shadowRadius: 0,
        zIndex: 10,
        elevation: 0,
      }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: 12, padding: 6 }}>
          <Feather name="arrow-left" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#1e293b', letterSpacing: 1, textShadowColor: 'rgba(255,255,255,0.7)', textShadowOffset: {width: 0, height: 1}, textShadowRadius: 2 }}>{station.id}</Text>
      </View>
      <ScrollView
        style={{ flex: 1, maxHeight: Platform.OS === 'web' ? '100vh' : undefined, overflowY: Platform.OS === 'web' ? 'auto' : undefined }}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 100 }}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
      >
        <View style={{ width: '100%', maxWidth: 600, paddingHorizontal: 20, marginTop: 28, alignSelf: 'center' }}>
          {/* Section: Receiver Info */}
          <Text style={{ fontSize: 16, fontWeight: '700', color: '#6366f1', marginBottom: 12, letterSpacing: 0.5 }}>Receiver Information</Text>
          {/* Receiver Station ID input with spinner */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Receiver Station ID</Text>
          <Text style={{ fontSize: 12, color: '#64748b', marginBottom: 6 }}>
            Try: ST-001, ST-002, ST-003, etc. (1-100+ available)
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 2 }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f1f5f9',
                borderRadius: 10,
                paddingHorizontal: 12,
                paddingVertical: 8,
                fontSize: 16,
                color: '#1e293b',
                borderStyle: 'dotted',
                borderWidth: 2,
                borderColor: showErrors && !receiverStationId ? '#ef4444' : '#cbd5e1',
              }}
              placeholder="Enter receiver station id (e.g., ST-001)"
              value={receiverStationId}
              onChangeText={handleReceiverStationIdChange}
              placeholderTextColor="#94a3b8"
            />
            {searchingStation && (
              <ActivityIndicator size="small" color="#6366f1" style={{ marginLeft: 8 }} />
            )}
          </View>
          {receiverStationId && !searchingStation ? (
            receiverStation ? (
              <Text style={{ color: '#334155', marginBottom: 10 }}>
                ✅ {receiverStation.name} • {receiverStation.hours}
              </Text>
            ) : (
              <Text style={{ color: '#ef4444', marginBottom: 10 }}>
                ❌ Station not found. Try ST-001 to ST-100+
              </Text>
            )
          ) : <View style={{ marginBottom: 10 }} />}
          {/* Receiver ID input with spinner */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Receiver ID</Text>
          <Text style={{ fontSize: 12, color: '#64748b', marginBottom: 6 }}>
            Try: RCV-001, RCV-002, RCV-003, etc. (1-55 available)
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 2 }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f1f5f9',
                borderRadius: 10,
                paddingHorizontal: 12,
                paddingVertical: 8,
                fontSize: 16,
                color: '#1e293b',
                borderStyle: 'dotted',
                borderWidth: 2,
                borderColor: showErrors && !receiverId ? '#ef4444' : '#cbd5e1',
              }}
              placeholder="Enter receiver id (e.g., RCV-001)"
              value={receiverId}
              onChangeText={handleReceiverIdChange}
              placeholderTextColor="#94a3b8"
            />
            {searchingReceiver && (
              <ActivityIndicator size="small" color="#6366f1" style={{ marginLeft: 8 }} />
            )}
          </View>
          {receiverId && !searchingReceiver ? (
            receiverObj ? (
              <Text style={{ color: '#334155', marginBottom: 18 }}>
                ✅ {receiverObj.name} • {receiverObj.email}
              </Text>
            ) : (
              <Text style={{ color: '#ef4444', marginBottom: 18 }}>
                ❌ Receiver not found. Try RCV-001 to RCV-055
              </Text>
            )
          ) : <View style={{ marginBottom: 18 }} />}
          <View style={{ height: 1, backgroundColor: '#e5e7eb', marginVertical: 12, borderRadius: 1 }} />
          {/* Section: Package Info */}
          <Text style={{ fontSize: 16, fontWeight: '700', color: '#6366f1', marginBottom: 12, letterSpacing: 0.5 }}>Package Information</Text>
          {/* Package Name */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Package Name</Text>
          <TextInput
            style={{
              backgroundColor: '#f1f5f9',
              borderRadius: 10,
              paddingHorizontal: 12,
              paddingVertical: 8,
              fontSize: 16,
              color: '#1e293b',
              borderStyle: 'dotted',
              borderWidth: 2,
              borderColor: showErrors && !packageName ? '#ef4444' : '#cbd5e1',
              marginBottom: 12,
            }}
            placeholder="Enter package name"
            value={packageName}
            onChangeText={setPackageName}
            placeholderTextColor="#94a3b8"
          />
          {/* Package Weight */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 8 }}>Weight</Text>
          <View style={{ flexDirection: 'column', marginBottom: 16 }}>
            {weightRanges.map((range, idx) => (
              <TouchableOpacity
                key={range}
                onPress={() => setPackageWeight(range)}
                style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 14, borderRadius: 12, paddingRight: 8, borderWidth: showErrors && !packageWeight ? 2 : 0, borderColor: showErrors && !packageWeight ? '#ef4444' : 'transparent', borderStyle: 'dotted' }}
                activeOpacity={0.7}
                accessibilityRole="radio"
                accessibilityState={{ selected: packageWeight === range }}
              >
                <View style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  borderWidth: 2,
                  borderColor: packageWeight === range ? '#6366f1' : '#cbd5e1',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 10,
                  backgroundColor: '#fff',
                }}>
                  {packageWeight === range && (
                    <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: '#6366f1' }} />
                  )}
                </View>
                <Text style={{ color: packageWeight === range ? '#6366f1' : '#334155', fontWeight: packageWeight === range ? 'bold' : '500', fontSize: 15 }}>{range}</Text>
              </TouchableOpacity>
            ))}
          </View>
          {/* Package Description (optional) */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Description (Optional)</Text>
          <TextInput
            style={{ backgroundColor: '#f1f5f9', borderRadius: 10, padding: 14, fontSize: 16, color: '#1e293b', marginBottom: 6, minHeight: 180, textAlignVertical: 'top', width: '100%', borderStyle: 'dotted', borderWidth: 2, borderColor: '#cbd5e1' }}
            placeholder="Enter description (optional)"
            value={packageDescription}
            onChangeText={handleDescriptionChange}
            multiline
            maxLength={2000}
            placeholderTextColor="#94a3b8"
          />
          <Text style={{ color: wordCount > maxWords ? '#ef4444' : '#64748b', fontSize: 13, alignSelf: 'center', marginBottom: 12 }}>{wordCount} / {maxWords} words</Text>
          {/* Package Image (optional) */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Package Image (Optional)</Text>
          <TouchableOpacity
            style={{ backgroundColor: '#f1f5f9', borderRadius: 10, borderStyle: 'dotted', borderWidth: 2, borderColor: '#cbd5e1', paddingVertical: 18, alignItems: 'center', marginBottom: 12 }}
            onPress={handlePickImage}
            activeOpacity={0.85}
          >
            <Feather name="image" size={28} color="#6366f1" style={{ marginBottom: 6 }} />
            <Text style={{ color: '#6366f1', fontWeight: 'bold', fontSize: 15 }}>Select Image</Text>
            {packageImage && (
              <Text style={{ color: '#64748b', fontSize: 13, marginTop: 6 }}>Tap to change image</Text>
            )}
          </TouchableOpacity>
          {packageImage ? (
            <View style={{ alignSelf: 'center', marginBottom: 16 }}>
              <Image source={{ uri: packageImage }} style={{ width: 120, height: 120, borderRadius: 12, borderWidth: 1, borderColor: '#e5e7eb' }} />
              <TouchableOpacity
                onPress={() => setPackageImage(null)}
                style={{ position: 'absolute', top: -10, right: -10, backgroundColor: '#fff', borderRadius: 999, padding: 2, elevation: 2 }}
                activeOpacity={0.8}
                accessibilityLabel="Remove image"
              >
                <Feather name="x-circle" size={26} color="#ef4444" />
              </TouchableOpacity>
            </View>
          ) : null}
          {/* Package Type */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Package Type</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginBottom: 16 }}>
            {packageTypes.map((type, idx) => (
              <TouchableOpacity
                key={type}
                onPress={() => setPackageType(type)}
                style={{ flexDirection: 'row', alignItems: 'center', marginRight: 18, marginBottom: 10, borderRadius: 12, paddingRight: 8, borderWidth: showErrors && !packageType ? 2 : 0, borderColor: showErrors && !packageType ? '#ef4444' : 'transparent', borderStyle: 'dotted' }}
                activeOpacity={0.7}
                accessibilityRole="radio"
                accessibilityState={{ selected: packageType === type }}
              >
                <View style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  borderWidth: 2,
                  borderColor: packageType === type ? '#6366f1' : '#cbd5e1',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 7,
                  backgroundColor: '#fff',
                }}>
                  {packageType === type && (
                    <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: '#6366f1' }} />
                  )}
                </View>
                <Text style={{ color: packageType === type ? '#6366f1' : '#334155', fontWeight: packageType === type ? 'bold' : '500', fontSize: 15 }}>{type}</Text>
              </TouchableOpacity>
            ))}
          </View>
          {/* Fragile/Handle With Care */}
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Text style={{ fontWeight: '600', color: '#1e293b', marginRight: 12 }}>Fragile/Handle With Care</Text>
            <Switch
              value={fragile}
              onValueChange={setFragile}
              thumbColor={fragile ? '#6366f1' : '#cbd5e1'}
              trackColor={{ false: '#cbd5e1', true: '#a5b4fc' }}
            />
          </View>
          {/* Package Value */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Package Value (UGX)</Text>
          <TextInput
            style={{
              backgroundColor: '#f1f5f9',
              borderRadius: 10,
              paddingHorizontal: 12,
              paddingVertical: 8,
              fontSize: 16,
              color: '#1e293b',
              borderStyle: 'dotted',
              borderWidth: 2,
              borderColor: showErrors && !packageValue ? '#ef4444' : '#cbd5e1',
              marginBottom: 12,
            }}
            placeholder="Enter package value in UGX"
            value={packageValue}
            onChangeText={setPackageValue}
            keyboardType="numeric"
            placeholderTextColor="#94a3b8"
          />
          <Text style={{ color: '#64748b', fontSize: 13, marginBottom: 12, marginTop: -8 }}>
            This value will not be shown to the traveller.
          </Text>
          {/* Transport Fare in UGX */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Transport Fare (UGX)</Text>
          <TextInput
            style={{
              backgroundColor: '#f1f5f9',
              borderRadius: 10,
              paddingHorizontal: 12,
              paddingVertical: 8,
              fontSize: 16,
              color: '#1e293b',
              borderStyle: 'dotted',
              borderWidth: 2,
              borderColor: showErrors && !fee ? '#ef4444' : '#cbd5e1',
              marginBottom: 12,
            }}
            placeholder="Enter transport fare in UGX"
            value={fee}
            onChangeText={setFee}
            keyboardType="numeric"
            placeholderTextColor="#94a3b8"
          />
          {/* Tip Traveller (optional) */}
          <Text style={{ fontWeight: '600', color: '#1e293b', marginBottom: 4 }}>Tip Traveller (Optional, UGX)</Text>
          <TextInput
            style={{ backgroundColor: '#f1f5f9', borderRadius: 10, paddingHorizontal: 12, paddingVertical: 8, fontSize: 16, color: '#1e293b', borderStyle: 'dotted', borderWidth: 2, borderColor: '#cbd5e1', marginBottom: 18 }}
            placeholder="Enter tip amount (optional)"
            value={tip}
            onChangeText={setTip}
            keyboardType="numeric"
            placeholderTextColor="#94a3b8"
          />
          <Text style={{ color: '#22c55e', fontSize: 15, fontWeight: 'bold', marginBottom: 18, marginTop: -6, textAlign: 'center' }}>
            Tip the traveller to encourage faster and friendlier service!
          </Text>
          {/* Spacer to ensure last input is visible above floating button */}
          <View style={{ height: 90 }} />
        </View>
      </ScrollView>
      {/* Floating Submit Button */}
      <View
        style={{
          position: Platform.OS === 'web' ? 'fixed' : 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'transparent',
          paddingVertical: 18,
          paddingHorizontal: 24,
          zIndex: 100,
          display: 'flex',
          alignItems: 'center',
          borderTopWidth: 0,
          shadowColor: 'transparent',
          shadowOpacity: 0,
          shadowRadius: 0,
          elevation: 0,
        }}
      >
        <TouchableOpacity
          style={{ backgroundColor: '#6366f1', borderRadius: 12, paddingVertical: 14, alignItems: 'center', width: '100%', maxWidth: 600, shadowColor: '#6366f1', shadowOpacity: 0.15, shadowRadius: 8, elevation: 2 }}
          activeOpacity={0.88}
          onPress={handleSubmit}
        >
                          <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 18, letterSpacing: 0.5 }}>
                  {isEditMode ? 'Update Package' : 'Submit Package'}
                </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddPackageScreen; 