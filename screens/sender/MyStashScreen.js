import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, ActivityIndicator, TouchableOpacity, Alert, Modal, TextInput, Image, Platform } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userPackageStorage, userStorage } from '../../utils/storage';

const EMPTY_ILLUSTRATION = 'https://cdn.jsdelivr.net/gh/ghkatende/cdn/fomo-empty.png'; // Use a nice illustration or replace with your own

const SEGMENTS = [
  { key: 'sending', label: 'Sending' },
  { key: 'receiving', label: 'Receiving' },
];

const MyStashScreen = () => {
  const navigation = useNavigation();
  const [packages, setPackages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentUserCode, setCurrentUserCode] = useState('');
  const [currentUserPhone, setCurrentUserPhone] = useState('');
  const [toast, setToast] = useState(null); // For success message
  const [segment, setSegment] = useState('sending');

  // Get current user's SR code and phone number
  useEffect(() => {
    const getUserData = async () => {
      try {
        // Try to get user data using the storage utility first
        let user = await userStorage.getUser();
        
        // If not found, try the alternative storage key
        if (!user) {
          const userString = await AsyncStorage.getItem('USER_STATE_V1');
          if (userString) {
            user = JSON.parse(userString);
          }
        }
        
        if (user) {
          setCurrentUserCode(user.userCode);
          setCurrentUserPhone(user.phoneNumber);
          console.log('Loaded user data in MyStash:', user);
        } else {
          console.log('No user data found in MyStash');
        }
      } catch (e) {
        console.log('Error getting user data:', e);
      }
    };
    getUserData();
  }, []);

  const loadPackages = async () => {
    setLoading(true);
    try {
      console.log('Loading packages for user:', { currentUserCode, currentUserPhone });
      
      // Get user data to ensure we have the role
      let user = await userStorage.getUser();
      
      // If not found, try the alternative storage key
      if (!user) {
        const userString = await AsyncStorage.getItem('USER_STATE_V1');
        if (userString) {
          user = JSON.parse(userString);
        }
      }
      
      console.log('Current user data:', user);
      
      if (!user || !user.userCode) {
        console.log('No user data available');
        setPackages([]);
        setLoading(false);
        return;
      }
      
      // Pass user ID and role explicitly
      const storedPackages = await userPackageStorage.getPackages(user.userCode, user.role || 'sender');
      console.log('Loaded packages:', storedPackages);
      setPackages(storedPackages);
    } catch (e) {
      console.log('Error loading packages:', e);
      setPackages([]);
    }
    setLoading(false);
  };

  useFocusEffect(
    React.useCallback(() => {
      loadPackages();
    }, [])
  );

  const handleEditPackage = (pkg) => {
    // Navigate to AddPackageScreen with edit mode and package data
    navigation.navigate('AddPackage', { 
      mode: 'edit', 
      packageData: pkg,
      stationId: pkg.receiverStationId // Use the receiver station as the current station
    });
  };

  const handlePackagePress = (pkg, type) => {
    if (type === 'sending') {
      // Navigate to package details screen with map
      navigation.navigate('PackageDetails', { 
        packageData: pkg,
        mode: 'sender'
      });
    } else {
      // For receiving packages, show basic details or different action
      Alert.alert('Package Details', `Package: ${pkg.name}\nID: ${pkg.id}\nStatus: Receiving`);
    }
  };

  const handleDeletePackage = (pkg) => {
    Alert.alert(
      'Delete Package',
      `Are you sure you want to delete "${pkg.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const user = await userStorage.getUser();
              if (!user) {
                const userString = await AsyncStorage.getItem('USER_STATE_V1');
                if (userString) {
                  user = JSON.parse(userString);
                }
              }
              
              if (user && user.userCode) {
                const success = await userPackageStorage.deletePackage(pkg.id, user.userCode, user.role || 'sender');
                if (success) {
                  console.log('Package deleted successfully');
                  setToast('Package deleted successfully');
                  setTimeout(() => setToast(null), 2000);
                  loadPackages(); // Reload packages
                } else {
                  Alert.alert('Error', 'Failed to delete package.');
                }
              }
            } catch (e) {
              console.log('Error deleting package:', e);
              Alert.alert('Error', 'Failed to delete package.');
            }
          }
        }
      ]
    );
  };

  // Helper to get status label and color
  const getStatusInfo = (pkg) => {
    const status = pkg.status || 'take_to_station';
    switch (status) {
      case 'take_to_station':
        return { label: 'Take to Station', color: '#6366f1', message: `Take this package to ${pkg.receiverStationId} for drop-off.`, fomo: true };
      case 'picked_by_traveller':
        return { label: 'Picked by Traveller', color: '#f59e42', message: 'A traveller has picked up your package.' };
      case 'at_receiver_station':
        return { label: 'At Receiver Station', color: '#0ea5e9', message: 'Your package has arrived at the receiver\'s station.' };
      case 'picked_by_receiver':
        return { label: 'Picked by Receiver', color: '#10b981', message: 'The receiver has picked up the package from the station.' };
      default:
        return { label: 'Unknown', color: '#64748b', message: '' };
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#6366f1" />
      </View>
    );
  }

  if (!packages || packages.length === 0) {
    return (
      <View className="flex-1 justify-center items-center bg-slate-50 px-6">
        <Image source={{ uri: EMPTY_ILLUSTRATION }} style={{ width: 220, height: 180, marginBottom: 24, opacity: 0.95 }} resizeMode="contain" />
        <Text className="text-2xl font-bold text-slate-800 mb-2 text-center">No Packages Yet</Text>
        <Text className="text-base text-slate-500 mb-6 text-center">Don't miss out! Send your first package today and enjoy fast, secure delivery. 🚀</Text>
        <TouchableOpacity
          className="flex-row items-center bg-indigo-500 px-6 py-3 rounded-xl shadow-lg"
          onPress={() => navigation.navigate('AddPackage', { stationId: null })}
        >
          <Feather name="plus" size={20} color="white" />
          <Text className="text-white font-bold text-base ml-2">Add Package</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Filter packages
  const sendingPackages = packages.filter(pkg => pkg.senderId === currentUserCode);
  const receivingPackages = packages.filter(pkg => pkg.receiverId === currentUserCode);

  // --- Header ---
  const Header = () => (
    <View className="flex-row items-center justify-between mb-6 mt-2 px-1">
      <View className="flex-row items-center">
        {/* Back button */}
        <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: 10, padding: 6 }}>
          <Feather name="arrow-left" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Feather name="package" size={28} color="#6366f1" style={{ marginRight: 10 }} />
        <View>
          <Text className="text-2xl font-extrabold text-slate-900">My Stash</Text>
          <Text className="text-base text-slate-500 font-medium">All your packages in one place</Text>
        </View>
      </View>
      <TouchableOpacity
        className="flex-row items-center bg-indigo-500 px-4 py-2 rounded-xl shadow"
        onPress={() => navigation.navigate('AddPackage', { stationId: null })}
      >
        <Feather name="plus" size={18} color="white" />
        <Text className="text-white font-bold text-base ml-2">Add</Text>
      </TouchableOpacity>
    </View>
  );

  // --- Segmented Control ---
  const SegmentControl = () => (
    <View className="flex-row bg-slate-100 rounded-xl mb-6 mx-1 p-1">
      {SEGMENTS.map(seg => (
        <TouchableOpacity
          key={seg.key}
          onPress={() => setSegment(seg.key)}
          style={{
            flex: 1,
            backgroundColor: segment === seg.key ? '#6366f1' : 'transparent',
            borderRadius: 12,
            paddingVertical: 10,
            alignItems: 'center',
            justifyContent: 'center',
            marginHorizontal: 2,
          }}
        >
          <Text style={{
            color: segment === seg.key ? 'white' : '#6366f1',
            fontWeight: 'bold',
            fontSize: 16,
            letterSpacing: 0.5,
          }}>{seg.label}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // --- Package Card ---
  const PackageCard = ({ pkg, type }) => {
    const statusInfo = getStatusInfo(pkg);
    const isFomo = statusInfo.fomo;
    return (
      <TouchableOpacity 
        onPress={() => handlePackagePress(pkg, type)}
        activeOpacity={0.93}
        style={{
          shadowColor: '#6366f1',
          shadowOpacity: 0.08,
          shadowRadius: 8,
          shadowOffset: { width: 0, height: 4 },
          elevation: 2,
          backgroundColor: 'white',
          borderRadius: 18,
          marginBottom: 18,
          borderWidth: 1,
          borderColor: '#e0e7ef',
        }}
      >
        <View className="p-5">
          <View className="flex-row justify-between items-start mb-2">
            <Text className="text-base font-bold text-slate-900 flex-1">{pkg.name}</Text>
            <View className={`px-2 py-1 rounded-full ${type === 'sending' ? 'bg-blue-100' : 'bg-green-100'}`}>
              <Text className={`text-xs font-semibold ${type === 'sending' ? 'text-blue-700' : 'text-green-700'}`}>
                {type === 'sending' ? 'Sending' : 'Receiving'}
              </Text>
            </View>
          </View>
          <Text className="text-xs text-slate-500 mb-1">ID: {pkg.id}</Text>
          <Text className="text-xs text-slate-400 mb-2">Created: {new Date(pkg.createdAt).toLocaleString()}</Text>
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-sm text-slate-600">Fare: UGX {pkg.fare}</Text>
            <Text className="text-sm text-slate-600">{pkg.weight} • {pkg.type}</Text>
          </View>

          {/* Status message above actions for sending packages */}
          {type === 'sending' && (
            <Text style={{ color: '#64748b', fontSize: 12, marginBottom: 6 }}>{statusInfo.message}</Text>
          )}

          {/* Action row: status badge + edit/delete */}
          {type === 'sending' && (
            <View className="flex-row items-center justify-between">
              {/* Status Badge (far left, styled like buttons) */}
              <View style={{
                backgroundColor: statusInfo.color + '22',
                borderColor: statusInfo.color,
                borderWidth: 1,
                borderRadius: 12,
                paddingVertical: 8,
                paddingHorizontal: 16,
                minWidth: 80,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 8,
                position: 'relative',
                overflow: 'visible',
              }}>
                <Text style={{ color: statusInfo.color, fontWeight: 'bold', fontSize: 13 }}>{statusInfo.label}</Text>
                {/* FOMO Pulse Animation */}
                {isFomo && (
                  <View style={{
                    position: 'absolute',
                    left: -8, top: -8, right: -8, bottom: -8,
                    borderRadius: 20,
                    borderWidth: 2,
                    borderColor: statusInfo.color,
                    opacity: 0.25,
                    zIndex: -1,
                    backgroundColor: statusInfo.color + '22',
                    shadowColor: statusInfo.color,
                    shadowOpacity: 0.5,
                    shadowRadius: 12,
                    shadowOffset: { width: 0, height: 0 },
                    elevation: 1,
                    // Animate pulse
                    transform: [{ scale: 1.1 }],
                  }} />
                )}
              </View>
              {/* Edit/Delete buttons (far right) */}
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation(); // Prevent card tap
                    handleEditPackage(pkg);
                  }}
                  className="flex-row items-center bg-blue-50 px-3 py-2 rounded-lg border border-blue-200"
                  style={{ marginRight: 8 }}
                >
                  <Feather name="edit-2" size={14} color="#3b82f6" />
                  <Text className="text-blue-600 text-xs font-medium ml-1">Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation(); // Prevent card tap
                    handleDeletePackage(pkg);
                  }}
                  className="flex-row items-center bg-red-50 px-3 py-2 rounded-lg border border-red-200"
                >
                  <Feather name="trash-2" size={14} color="#ef4444" />
                  <Text className="text-red-600 text-xs font-medium ml-1">Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // --- Main Render ---
  let shownPackages = segment === 'sending' ? sendingPackages : receivingPackages;
  let emptyText = segment === 'sending'
    ? "You have no packages to send. Don't miss today's delivery window!"
    : 'No packages to receive yet. Stay tuned for updates!';
  let emptyColor = segment === 'sending' ? '#6366f1' : '#10b981';

  return (
    <View className="flex-1 bg-slate-50 px-2 pt-2" style={{ position: 'relative' }}>
      <Header />
      <SegmentControl />
      {shownPackages.length === 0 && (
        <Text className="text-center font-semibold mb-6" style={{ color: emptyColor }}>{emptyText}</Text>
      )}
      {shownPackages.map(pkg => (
        <PackageCard key={pkg.id} pkg={pkg} type={segment} />
      ))}
      {/* Toast/Snackbar */}
      {toast && (
        <View style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 30,
          alignItems: 'center',
          zIndex: 100,
        }}>
          <View style={{
            backgroundColor: '#10b981',
            paddingHorizontal: 24,
            paddingVertical: 12,
            borderRadius: 24,
            shadowColor: '#000',
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}>
            <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 15 }}>{toast}</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default MyStashScreen; 