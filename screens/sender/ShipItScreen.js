import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, TextInput, FlatList, ActivityIndicator } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { stations } from './stationsData';

const distanceFilters = [
  { label: '< 2 mi', value: 2 },
  { label: '< 5 mi', value: 5 },
];

const PAGE_SIZE = 20;

const ShipItScreen = () => {
  const [search, setSearch] = useState('');
  const [hourFilter, setHourFilter] = useState('all'); // 'all', '24hrs', 'openNow'
  const [distanceFilter, setDistanceFilter] = useState(null); // null or number
  const [visibleCount, setVisibleCount] = useState(PAGE_SIZE);
  const [loadingMore, setLoadingMore] = useState(false);
  const navigation = useNavigation();

  // Filtering logic
  const filteredStations = stations.filter(station => {
    const matchesSearch =
      station.name.toLowerCase().includes(search.toLowerCase()) ||
      station.id.toLowerCase().includes(search.toLowerCase());
    const matchesHour =
      hourFilter === 'all' ||
      (hourFilter === '24hrs' && station.hours === '24hrs') ||
      (hourFilter === 'openNow' && station.openNow);
    const matchesDistance =
      distanceFilter == null || station.distance <= distanceFilter;
    return matchesSearch && matchesHour && matchesDistance;
  });

  const visibleStations = filteredStations.slice(0, visibleCount);

  const handleLoadMore = () => {
    if (visibleCount < filteredStations.length) {
      setLoadingMore(true);
      setTimeout(() => {
        setVisibleCount(prev => Math.min(prev + PAGE_SIZE, filteredStations.length));
        setLoadingMore(false);
      }, 500);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <Text className="text-2xl font-extrabold text-slate-900 mb-4 mt-6 ml-4">Choose a Station Near You</Text>
      {/* Search Bar */}
      <View className="flex-row items-center bg-white rounded-xl shadow px-4 py-2 mb-4 border border-slate-100 mx-4">
        <Feather name="search" size={20} color="#a5b4fc" className="mr-2" />
        <TextInput
          className="flex-1 text-base text-slate-900"
          placeholder="Search by name or ID..."
          value={search}
          onChangeText={setSearch}
          placeholderTextColor="#bbb"
        />
      </View>
      {/* Filter Chips */}
      <View className="flex-row space-x-2 mb-5 max-w-xl mx-auto">
        <TouchableOpacity
          className={`px-3 py-1 rounded-full border ${hourFilter === 'all' ? 'bg-indigo-500 border-indigo-500' : 'bg-white border-slate-200'} mr-1`}
          onPress={() => setHourFilter('all')}
        >
          <Text className={`text-xs font-semibold ${hourFilter === 'all' ? 'text-white' : 'text-slate-700'}`}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`px-3 py-1 rounded-full border ${hourFilter === '24hrs' ? 'bg-indigo-500 border-indigo-500' : 'bg-white border-slate-200'} mr-1`}
          onPress={() => setHourFilter('24hrs')}
        >
          <Text className={`text-xs font-semibold ${hourFilter === '24hrs' ? 'text-white' : 'text-slate-700'}`}>24hrs</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`px-3 py-1 rounded-full border ${hourFilter === 'openNow' ? 'bg-indigo-500 border-indigo-500' : 'bg-white border-slate-200'} mr-3`}
          onPress={() => setHourFilter('openNow')}
        >
          <Text className={`text-xs font-semibold ${hourFilter === 'openNow' ? 'text-white' : 'text-slate-700'}`}>Open Now</Text>
        </TouchableOpacity>
        {distanceFilters.map(f => (
          <TouchableOpacity
            key={f.value}
            className={`px-3 py-1 rounded-full border ${distanceFilter === f.value ? 'bg-green-500 border-green-500' : 'bg-white border-slate-200'}`}
            onPress={() => setDistanceFilter(f.value)}
          >
            <Text className={`text-xs font-semibold ${distanceFilter === f.value ? 'text-white' : 'text-slate-700'}`}>{f.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {/* Station List with FlatList */}
      <FlatList
        data={visibleStations}
        keyExtractor={item => item.id}
        renderItem={({ item: station }) => (
        <TouchableOpacity
          key={station.id}
            className="flex-row bg-white rounded-xl shadow-lg mb-3 p-5 items-center border border-slate-100 mx-4"
          activeOpacity={0.92}
          onPress={() => navigation.navigate('StationMap', { station })}
        >
          <Image source={station.image} className="w-24 h-20 rounded-xl mr-2 bg-slate-200 self-center" resizeMode="cover" />
          <View className="flex-1 min-w-0">
            <View className="flex-row items-center mb-1 min-w-0">
              <Text className="text-base font-extrabold text-slate-900 tracking-tight mr-2 flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.name}</Text>
            </View>
            <Text className="text-xs text-slate-400 font-medium mb-2 min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.id}</Text>
            <View className="flex-row items-center space-x-2 min-w-0">
              <Feather name="clock" size={13} color="#6366f1" className="mr-1" />
              <Text className="text-xs text-slate-500 font-semibold mr-2 flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.hours}</Text>
              <MaterialCommunityIcons name="walk" size={14} color="#22c55e" style={{ marginRight: 2 }} />
              <Text className="text-xs text-slate-500 font-semibold flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.distance} miles</Text>
              {/* Status Badge */}
              {station.openNow ? (
                <View className="bg-green-100 px-2 py-0.5 rounded-full ml-2">
                  <Text className="text-xs font-bold text-green-600 min-w-0" numberOfLines={1} ellipsizeMode="tail">Open Now</Text>
                </View>
              ) : station.hours === '24hrs' ? (
                <View className="bg-gray-200 px-2 py-0.5 rounded-full ml-2">
                  <Text className="text-xs font-bold text-gray-500 min-w-0" numberOfLines={1} ellipsizeMode="tail">Inactive</Text>
                </View>
              ) : (
                <View className="bg-red-100 px-2 py-0.5 rounded-full ml-2">
                  <Text className="text-xs font-bold text-red-600 min-w-0" numberOfLines={1} ellipsizeMode="tail">Closed</Text>
                </View>
              )}
            </View>
          </View>
        </TouchableOpacity>
        )}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={loadingMore ? <ActivityIndicator size="small" color="#6366f1" style={{ marginVertical: 16 }} /> : null}
        ListEmptyComponent={<Text className="text-center text-slate-400 mt-10">No stations found.</Text>}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 32 }}
      />
    </View>
  );
};

export default ShipItScreen;