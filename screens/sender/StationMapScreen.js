import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, Image, Platform, ActivityIndicator } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import DeliveryMapView from './DeliveryMapView';

const StationMapScreen = () => {
  const route = useRoute();
  const { station } = route.params;
  const navigation = useNavigation();
  const [cardExpanded, setCardExpanded] = useState(false);
  const [loadingRoute, setLoadingRoute] = useState(false); // No real route loading now

  // Placeholder data for views, ratings, agent name
  const views = 124;
  const rating = 4.7;
  const agentName = 'Jane Doe';

  // Determine badge
  let badge, badgeColor, badgeBg;
  if (station.openNow) {
    badge = 'Open Now'; badgeColor = 'text-green-600'; badgeBg = 'bg-green-100';
  } else if (station.hours === '24hrs') {
    badge = 'Inactive'; badgeColor = 'text-gray-500'; badgeBg = 'bg-gray-200';
  } else {
    badge = 'Closed'; badgeColor = 'text-red-600'; badgeBg = 'bg-red-100';
  }

  return (
    <SafeAreaView className="flex-1 bg-slate-50" style={{ position: 'relative' }}>
      {/* Map (always show DeliveryMapView for consistency) */}
      <DeliveryMapView station={station}>
        {/* Floating Station Info Card */}
        <View className="absolute left-0 right-0 bottom-0 px-4 pb-6" style={{ height: Platform.OS === 'web' ? 420 : cardExpanded ? 420 : 90, zIndex: 10 }}>
          <View className="bg-white rounded-b-3xl shadow-2xl border-2 border-indigo-100 w-full max-w-xl mx-auto overflow-hidden">
            {/* Card Header: Station Image (always show on web, only expanded on native) */}
            {(Platform.OS === 'web' || cardExpanded) && (
              <View className="w-full relative">
                <Image source={station.image} className="w-full h-48 bg-slate-200" resizeMode="cover" style={{ margin: 0, padding: 0 }} />
                {Platform.OS !== 'web' && (
                  <TouchableOpacity
                    style={{ position: 'absolute', bottom: 10, right: 10, backgroundColor: 'rgba(255,255,255,0.85)', borderRadius: 999, padding: 8 }}
                    activeOpacity={0.8}
                    accessible accessibilityRole="button" accessibilityLabel="Zoom image"
                    onPress={() => { /* TODO: Implement zoom modal if needed */ }}
                  >
                    <Feather name="zoom-in" size={22} color="#6366f1" />
                  </TouchableOpacity>
                )}
              </View>
            )}
            {/* Card Body */}
            <View className={`p-6 pt-4 ${Platform.OS === 'web' || cardExpanded ? '' : 'flex-row items-center justify-between'}`}> 
              {/* Title and Badge Row */}
              <View className="flex-row items-center justify-between min-w-0">
                <Text className="text-lg font-extrabold text-slate-900 tracking-tight flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.name}</Text>
                <View className={`${badgeBg} px-2 py-0.5 rounded-full ml-2`}>
                  <Text className={`text-xs font-bold ${badgeColor} min-w-0`} numberOfLines={1} ellipsizeMode="tail">{badge}</Text>
                </View>
              </View>
              {/* Always show all details on web, only expanded on native */}
              {(Platform.OS === 'web' || cardExpanded) &&
                <>
                  <Text className="text-xs text-slate-400 font-medium mb-2 min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.id}</Text>
                  <View className="flex-row items-center space-x-2 min-w-0 mb-2">
                    <Feather name="clock" size={13} color="#6366f1" className="mr-1" />
                    <Text className="text-xs text-slate-500 font-semibold mr-2 flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.hours}</Text>
                    <MaterialCommunityIcons name="walk" size={14} color="#22c55e" style={{ marginRight: 2 }} />
                    <Text className="text-xs text-slate-500 font-semibold flex-shrink min-w-0" numberOfLines={1} ellipsizeMode="tail">{station.distance} miles</Text>
                  </View>
                  <View className="flex-row items-center space-x-4 mb-3">
                    <View className="flex-row items-center">
                      <Feather name="eye" size={16} color="#a5b4fc" className="mr-1" />
                      <Text className="text-xs text-slate-500 font-semibold">{views} views</Text>
                    </View>
                    <View className="flex-row items-center">
                      <Feather name="star" size={16} color="#facc15" className="mr-1" />
                      <Text className="text-xs text-slate-500 font-semibold">{rating} rating</Text>
                    </View>
                    <View className="flex-row items-center">
                      <Feather name="user" size={16} color="#6366f1" className="mr-1" />
                      <Text className="text-xs text-slate-500 font-semibold">Agent: {agentName}</Text>
                    </View>
                  </View>
                  {/* Add Package Button: Only enabled if station is open */}
                  {station.openNow ? (
                    <TouchableOpacity className="w-full py-3 rounded-xl bg-indigo-500 items-center mt-2" activeOpacity={0.88} onPress={() => navigation.navigate('AddPackage', { stationId: station.id })}>
                      <Text className="text-white text-base font-bold">Add Package to This Station</Text>
                    </TouchableOpacity>
                  ) : (
                    <View className="w-full py-3 rounded-xl bg-gray-300 items-center mt-2 opacity-60">
                      <Text className="text-white text-base font-bold">Station Closed</Text>
                    </View>
                  )}
                </>
              }
              {/* Chevron for collapse/expand (native only) */}
              {Platform.OS !== 'web' && (
                <View className="absolute left-0 right-0 -top-5 items-center z-20">
                  <View className="w-10 h-1.5 rounded-full bg-slate-300" />
                </View>
              )}
            </View>
          </View>
        </View>
      </DeliveryMapView>
      {/* Loading indicator for route (if needed in future) */}
      {loadingRoute && Platform.OS !== 'web' && (
        <View style={{ position: 'absolute', top: 60, left: 0, right: 0, alignItems: 'center', zIndex: 20 }}>
          <ActivityIndicator size="small" color="#6366f1" />
          <Text className="text-xs text-slate-500 mt-1">Loading route...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

export default StationMapScreen; 