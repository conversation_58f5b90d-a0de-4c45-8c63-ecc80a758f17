import React from 'react';
import { View, Text, Image } from 'react-native';

const IllustratorStep = ({ title, description, illustration }) => (
  <View className="flex-1 w-full items-center justify-between px-6 pt-10 pb-4">
    {/* Illustration */}
    <Image
      source={illustration}
      className="w-56 h-56 mb-2"
      resizeMode="contain"
      style={{ marginTop: 16 }}
    />
    {/* Title & Description */}
    <View className="items-center mt-2">
      <Text className="text-xl font-extrabold text-indigo-600 mb-2 text-center">{title}</Text>
      <Text className="text-base text-slate-500 text-center max-w-xs">{description}</Text>
    </View>
  </View>
);

export default IllustratorStep; 