import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import Feather from 'react-native-vector-icons/Feather';
import { useAuth } from '../AuthContext';

const CustomDrawer = (props) => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <DrawerContentScrollView {...props} style={styles.drawer}>
      {/* User Info Section */}
      <View style={styles.userSection}>
        <View style={styles.avatar}>
          <Feather name="user" size={32} color="#6366f1" />
        </View>
        <Text style={styles.userName}>{user?.username || 'User'}</Text>
        <Text style={styles.userCode}>{user?.userCode || 'SR:XXXXX'}</Text>
        <Text style={styles.userRole}>{user?.role || 'Unknown Role'}</Text>
      </View>

      {/* Profile Menu Item (for sender/receiver) */}
      {(user?.role && (user.role.toLowerCase().includes('sender') || user.role.toLowerCase().includes('receiver'))) && (
        <TouchableOpacity
          style={[
            styles.navItem,
            props.state.routes[props.state.index]?.name === 'Profile' && styles.navItemActive,
            { marginTop: 10, marginBottom: 2 }
          ]}
          onPress={() => props.navigation.navigate('Profile')}
        >
          <Feather name="user" size={20} color={props.state.routes[props.state.index]?.name === 'Profile' ? '#6366f1' : '#64748b'} />
          <Text style={[
            styles.navText,
            props.state.routes[props.state.index]?.name === 'Profile' && styles.navTextActive
          ]}>
            Profile
          </Text>
        </TouchableOpacity>
      )}

      {/* Navigation Items */}
      <View style={styles.navigationSection}>
        {props.state.routes.map((route, index) => {
          const isFocused = props.state.index === index;
          const iconName = route.name === 'Tabs' ? 'home' : 
                          route.name === 'Notifications' ? 'bell' : 'circle';
          
          return (
            <TouchableOpacity
              key={route.key}
              style={[styles.navItem, isFocused && styles.navItemActive]}
              onPress={() => props.navigation.navigate(route.name)}
            >
              <Feather 
                name={iconName} 
                size={20} 
                color={isFocused ? '#6366f1' : '#64748b'} 
              />
              <Text style={[styles.navText, isFocused && styles.navTextActive]}>
                {route.name === 'Tabs' ? 'Home' : route.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Logout Section */}
      <View style={styles.logoutSection}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Feather name="log-out" size={20} color="#ef4444" />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </DrawerContentScrollView>
  );
};

const styles = StyleSheet.create({
  drawer: {
    backgroundColor: '#ffffff',
  },
  userSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f1f5f9',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  userCode: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '600',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 12,
    color: '#64748b',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  navigationSection: {
    paddingTop: 20,
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginHorizontal: 8,
    borderRadius: 8,
  },
  navItemActive: {
    backgroundColor: '#f1f5f9',
  },
  navText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
  navTextActive: {
    color: '#6366f1',
    fontWeight: '600',
  },
  logoutSection: {
    marginTop: 'auto',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  logoutText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '600',
  },
});

export default CustomDrawer; 