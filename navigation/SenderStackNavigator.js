import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import SenderTabNavigator from './SenderTabNavigator';
import { TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import StationMapScreen from '../screens/sender/StationMapScreen';
import AddPackageScreen from '../screens/sender/AddPackageScreen';
import PackageDetailsScreen from '../screens/sender/PackageDetailsScreen';
import ProfileScreen from '../screens/sender/ProfileScreen';

const Stack = createStackNavigator();

const SenderStackNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Tabs" component={SenderTabNavigator} />
    <Stack.Screen name="StationMap" component={StationMapScreen} options={{ headerShown: true, title: 'Station Map' }} />
    <Stack.Screen 
      name="AddPackage" 
      component={AddPackageScreen} 
      options={({ navigation }) => ({ 
        title: 'Add Package',
        headerLeft: () => (
          <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginLeft: 16 }}>
            <Feather name="arrow-left" size={24} color="#6366f1" />
          </TouchableOpacity>
        ),
      })} 
    />
    <Stack.Screen 
      name="PackageDetails" 
      component={PackageDetailsScreen} 
      options={{ headerShown: false }} 
    />
    <Stack.Screen 
      name="Profile" 
      component={ProfileScreen} 
      options={{ headerShown: false }} 
    />
  </Stack.Navigator>
);

export default SenderStackNavigator; 