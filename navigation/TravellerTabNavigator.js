import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { TouchableOpacity } from 'react-native';
import GrabGoScreen from '../screens/traveller/GrabGoScreen';
import MyRunsScreen from '../screens/traveller/MyRunsScreen';
import FlexScreen from '../screens/traveller/FlexScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import CustomDrawer from '../components/CustomDrawer';

const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

function TravellerTabs({ navigation }) {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerTitle: '',
        tabBarIcon: ({ color, size }) => {
          if (route.name === 'Grab & Go') return <Feather name="shopping-bag" size={size} color={color} />;
          if (route.name === 'My Runs') return <MaterialCommunityIcons name="walk" size={size} color={color} />;
          if (route.name === 'Flex') return <Feather name="activity" size={size} color={color} />;
        },
        headerLeft: () =>
          route.name === 'Grab & Go' ? (
            <TouchableOpacity className="ml-4" onPress={() => navigation.openDrawer()}>
              <Feather name="menu" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
        headerRight: () =>
          route.name === 'Grab & Go' ? (
            <TouchableOpacity className="mr-4" onPress={() => navigation.navigate('Notifications')}>
              <Feather name="bell" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
      })}
    >
      <Tab.Screen name="Grab & Go" component={GrabGoScreen} />
      <Tab.Screen name="My Runs" component={MyRunsScreen} />
      <Tab.Screen name="Flex" component={FlexScreen} />
    </Tab.Navigator>
  );
}

const TravellerTabNavigator = () => {
  return (
    <Drawer.Navigator 
      drawerContent={(props) => <CustomDrawer {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <Drawer.Screen name="Tabs" component={TravellerTabs} />
      <Drawer.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ headerShown: false }}
      />
    </Drawer.Navigator>
  );
};

export default TravellerTabNavigator; 