import React from 'react';
import SenderStackNavigator from './SenderStackNavigator';
import TravellerTabNavigator from './TravellerTabNavigator';
import AgentTabNavigator from './AgentTabNavigator';
import { View, Text } from 'react-native';
import { useAuth } from '../AuthContext';

const AppNavigator = () => {
  const { role } = useAuth();
  console.log('AppNavigator role:', role);
  if (role === 'Sender/Receiver') {
    return <SenderStackNavigator />;
  }
  if (role === 'Traveller') {
    return <TravellerTabNavigator />;
  }
  if (role === 'Agent') {
    return <AgentTabNavigator />;
  }
  // Fallback error screen
  return (
    <View className="flex-1 justify-center items-center">
      <Text className="text-red-600 text-lg">Unknown role: {String(role)}</Text>
    </View>
  );
};

export default AppNavigator; 