import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import Feather from 'react-native-vector-icons/Feather';
import { TouchableOpacity } from 'react-native';
import HomeScreen from '../screens/agent/HomeScreen';
import VerifyTransactionScreen from '../screens/agent/VerifyTransactionScreen';
import ProfileScreen from '../screens/agent/ProfileScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import CustomDrawer from '../components/CustomDrawer';

const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

function AgentTabs({ navigation }) {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerTitle: '',
        tabBarIcon: ({ color, size }) => {
          let iconName;
          if (route.name === 'Home') iconName = 'shield';
          else if (route.name === 'Verify') iconName = 'check-circle';
          else if (route.name === 'Profile') iconName = 'user';
          return <Feather name={iconName} size={size} color={color} />;
        },
        headerLeft: () =>
          route.name === 'Home' ? (
            <TouchableOpacity className="ml-4" onPress={() => navigation.openDrawer()}>
              <Feather name="menu" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
        headerRight: () =>
          route.name === 'Home' ? (
            <TouchableOpacity className="mr-4" onPress={() => navigation.navigate('Notifications')}>
              <Feather name="bell" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Verify" component={VerifyTransactionScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

const AgentTabNavigator = () => {
  return (
    <Drawer.Navigator 
      drawerContent={(props) => <CustomDrawer {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <Drawer.Screen name="Tabs" component={AgentTabs} />
      <Drawer.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ headerShown: false }}
      />
    </Drawer.Navigator>
  );
};

export default AgentTabNavigator; 