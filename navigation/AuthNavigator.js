import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import LoginScreen from '../screens/LoginScreen';
import OtpScreen from '../screens/OtpScreen';
import RoleSelectorScreen from '../screens/RoleSelectorScreen';
import IllustratorScreen from '../screens/IllustratorScreen';

const Stack = createStackNavigator();

const AuthNavigator = ({ onLogin }) => {
  return (
    <Stack.Navigator 
      initialRouteName="RoleSelector"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="RoleSelector" component={RoleSelectorScreen} />
      <Stack.Screen name="Illustrator">
        {(props) => <IllustratorScreen {...props} onLogin={onLogin} />}
      </Stack.Screen>
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Otp" component={OtpScreen} />
    </Stack.Navigator>
  );
};

export default AuthNavigator; 