import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import Feather from 'react-native-vector-icons/Feather';
import { TouchableOpacity } from 'react-native';
import DashboardScreen from '../screens/sender/DashboardScreen';
import ShipItScreen from '../screens/sender/ShipItScreen';
import MyStashScreen from '../screens/sender/MyStashScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import CustomDrawer from '../components/CustomDrawer';

const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

function SenderTabs({ navigation }) {
  return (
    <Tab.Navigator
      initialRouteName="Ship It"
      screenOptions={({ route }) => ({
        headerTitle: '',
        tabBarIcon: ({ color, size }) => {
          let iconName;
          if (route.name === 'Dashboard') iconName = 'home';
          else if (route.name === 'Ship It') iconName = 'send';
          else if (route.name === 'My Stash') iconName = 'archive';
          return <Feather name={iconName} size={size} color={color} />;
        },
        headerLeft: () =>
          route.name === 'Dashboard' ? (
            <TouchableOpacity className="ml-4" onPress={() => navigation.openDrawer()}>
              <Feather name="menu" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
        headerRight: () =>
          route.name === 'Dashboard' ? (
            <TouchableOpacity className="mr-4" onPress={() => navigation.navigate('Notifications')}>
              <Feather name="bell" size={24} color="#222" />
            </TouchableOpacity>
          ) : null,
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Ship It" component={ShipItScreen} />
      <Tab.Screen name="My Stash" component={MyStashScreen} />
    </Tab.Navigator>
  );
}

const SenderTabNavigator = () => {
  return (
    <Drawer.Navigator 
      drawerContent={(props) => <CustomDrawer {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <Drawer.Screen name="Tabs" component={SenderTabs} />
      <Drawer.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ headerShown: false }}
      />
    </Drawer.Navigator>
  );
};

export default SenderTabNavigator; 