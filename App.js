import 'react-native-gesture-handler';
import './global.css'; // Import Tailwind CSS for web
import React, { useState, useEffect, useRef } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import AuthNavigator from './navigation/AuthNavigator';
import AppNavigator from './navigation/AppNavigator';
// Import all screens (for navigation)
import IllustratorScreen from './screens/IllustratorScreen';
import NotificationsScreen from './screens/NotificationsScreen';
import OtpScreen from './screens/OtpScreen';
import LoginScreen from './screens/LoginScreen';
import RoleSelectorScreen from './screens/RoleSelectorScreen';
// Sender screens
import SenderDashboardScreen from './screens/sender/DashboardScreen';
import SenderMyStashScreen from './screens/sender/MyStashScreen';
import SenderShipItScreen from './screens/sender/ShipItScreen';
// Traveller screens
import TravellerProfileScreen from './screens/traveller/ProfileScreen';
import TravellerFindJobsScreen from './screens/traveller/FindJobsScreen';
import TravellerGrabGoScreen from './screens/traveller/GrabGoScreen';
import TravellerMyRunsScreen from './screens/traveller/MyRunsScreen';
import TravellerHomeScreen from './screens/traveller/HomeScreen';
import TravellerFlexScreen from './screens/traveller/FlexScreen';
// Agent screens
import AgentProfileScreen from './screens/agent/ProfileScreen';
import AgentHomeScreen from './screens/agent/HomeScreen';
import AgentVerifyTransactionScreen from './screens/agent/VerifyTransactionScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, ActivityIndicator, Image, View, Text } from 'react-native';
import { AuthProvider, useAuth } from './AuthContext';

// DEV_SCREEN options:
// '' (empty string) for normal navigation
// 'Illustrator', 'Notifications', 'Otp', 'Login', 'RoleSelector'
// 'SenderDashboard', 'SenderMyStash', 'SenderShipIt'
// 'TravellerProfile', 'TravellerFindJobs', 'TravellerGrabGo', 'TravellerMyRuns', 'TravellerHome', 'TravellerFlex'
// 'AgentProfile', 'AgentHome', 'AgentVerifyTransaction'
const DEV_SCREEN = '';
const PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';
const PERSISTENCE_USER_KEY = 'USER_STATE_V1';

function MainApp() {
  const { user } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const [initialState, setInitialState] = useState();
  const navigationRef = useRef();

  useEffect(() => {
    const restoreState = async () => {
      try {
        let savedStateString;
        if (Platform.OS === 'web') {
          savedStateString = window.localStorage.getItem(PERSISTENCE_KEY);
        } else {
          savedStateString = await AsyncStorage.getItem(PERSISTENCE_KEY);
        }
        const state = savedStateString ? JSON.parse(savedStateString) : undefined;
        if (state !== undefined) {
          setInitialState(state);
        }
      } finally {
        setIsReady(true);
      }
    };
    restoreState();
  }, []);

  if (!isReady) {
    return (
      <SafeAreaProvider>
        <View className="flex-1 bg-white justify-center items-center">
          <Image source={require('./assets/icon.png')} style={{ width: 80, height: 80, marginBottom: 24 }} resizeMode="contain" />
          <Text className="text-3xl font-extrabold text-slate-900 mb-2 tracking-tight">Trancpota</Text>
          <Text className="text-base text-slate-500 mb-6 text-center">Cheap to send, quick to receive.</Text>
          <ActivityIndicator size="large" color="#6366f1" />
        </View>
      </SafeAreaProvider>
    );
  }

  // DEV SCREEN SWITCH
  if (DEV_SCREEN === 'Illustrator') return <SafeAreaProvider><IllustratorScreen route={{ params: { role: 'Sender/Receiver' } }} onLogin={() => {}} /></SafeAreaProvider>;
  if (DEV_SCREEN === 'Notifications') return <SafeAreaProvider><NotificationsScreen navigation={{ navigate: () => {}, goBack: () => {}, canGoBack: () => true }} /></SafeAreaProvider>;
  if (DEV_SCREEN === 'Otp') return <SafeAreaProvider><OtpScreen route={{ params: { role: 'Sender/Receiver', phoneNumber: '+256712345678' } }} navigation={{ navigate: () => {} }} /></SafeAreaProvider>;
  if (DEV_SCREEN === 'Login') return <SafeAreaProvider><LoginScreen route={{ params: { role: 'Sender/Receiver' } }} navigation={{ navigate: () => {} }} /></SafeAreaProvider>;
  if (DEV_SCREEN === 'RoleSelector') return <SafeAreaProvider><RoleSelectorScreen navigation={{ navigate: () => {} }} /></SafeAreaProvider>;
  // Sender
  if (DEV_SCREEN === 'SenderDashboard') return <SafeAreaProvider><SenderDashboardScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'SenderMyStash') return <SafeAreaProvider><SenderMyStashScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'SenderShipIt') return <SafeAreaProvider><SenderShipItScreen /></SafeAreaProvider>;
  // Traveller
  if (DEV_SCREEN === 'TravellerProfile') return <SafeAreaProvider><TravellerProfileScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'TravellerFindJobs') return <SafeAreaProvider><TravellerFindJobsScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'TravellerGrabGo') return <SafeAreaProvider><TravellerGrabGoScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'TravellerMyRuns') return <SafeAreaProvider><TravellerMyRunsScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'TravellerHome') return <SafeAreaProvider><TravellerHomeScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'TravellerFlex') return <SafeAreaProvider><TravellerFlexScreen /></SafeAreaProvider>;
  // Agent
  if (DEV_SCREEN === 'AgentProfile') return <SafeAreaProvider><AgentProfileScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'AgentHome') return <SafeAreaProvider><AgentHomeScreen /></SafeAreaProvider>;
  if (DEV_SCREEN === 'AgentVerifyTransaction') return <SafeAreaProvider><AgentVerifyTransactionScreen /></SafeAreaProvider>;

  // Default navigation
  return (
    <SafeAreaProvider>
      <NavigationContainer
        ref={navigationRef}
        initialState={initialState}
        onStateChange={async state => {
          const stateString = JSON.stringify(state);
          if (Platform.OS === 'web') {
            window.localStorage.setItem(PERSISTENCE_KEY, stateString);
          } else {
            await AsyncStorage.setItem(PERSISTENCE_KEY, stateString);
          }
        }}
      >
        {user ? <AppNavigator /> : <AuthNavigator />}
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

export default function App() {
  return (
    <AuthProvider>
      <MainApp />
    </AuthProvider>
  );
}
