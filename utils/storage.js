import AsyncStorage from '@react-native-async-storage/async-storage';

// Base storage keys
export const STORAGE_KEYS = {
  USER_DATA: 'USER_DATA_V1',
  USER_PREFERENCES: 'USER_PREFERENCES_V1',
  APP_SETTINGS: 'APP_SETTINGS_V1',
};

// User-specific storage keys generator
export const getUserStorageKeys = (userId, role) => {
  const rolePrefix = role ? `${role.toUpperCase().replace('/', '_')}_` : '';
  const userPrefix = userId ? `${userId}_` : '';
  return {
    PACKAGES: `${userPrefix}${rolePrefix}PACKAGES_V1`,
    NOTIFICATIONS: `${userPrefix}${rolePrefix}NOTIFICATIONS_V1`,
    RECENT_SEARCHES: `${userPrefix}${rolePrefix}RECENT_SEARCHES_V1`,
    FAVORITE_STATIONS: `${userPrefix}${rolePrefix}FAVORITE_STATIONS_V1`,
    USER_PREFERENCES: `${userPrefix}${rolePrefix}USER_PREFERENCES_V1`,
    USER_PROFILE: `${userPrefix}USER_PROFILE_V1`,
    USER_ACTIVITY: `${userPrefix}USER_ACTIVITY_V1`,
  };
};

// Get current user's ID and role from storage
export const getCurrentUserInfo = async () => {
  try {
    const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
    if (userData) {
      const user = JSON.parse(userData);
      return { userId: user.userCode, role: user.role };
    }
    return { userId: null, role: null };
  } catch (error) {
    console.error('Error getting current user info:', error);
    return { userId: null, role: null };
  }
};

// User Data Management (Global - not role-specific)
export const userStorage = {
  async saveUser(userData) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      return true;
    } catch (error) {
      console.error('Error saving user data:', error);
      return false;
    }
  },

  async getUser() {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  },

  async removeUser() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
      return true;
    } catch (error) {
      console.error('Error removing user data:', error);
      return false;
    }
  },
};

// User-specific Package Management
export const userPackageStorage = {
  async savePackages(packages, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for package storage');
        return false;
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId, currentUser.role);
      await AsyncStorage.setItem(userKeys.PACKAGES, JSON.stringify(packages));
      return true;
    } catch (error) {
      console.error('Error saving user packages:', error);
      return false;
    }
  },

  async getPackages(userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for package storage');
        return [];
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId, currentUser.role);
      const packages = await AsyncStorage.getItem(userKeys.PACKAGES);
      return packages ? JSON.parse(packages) : [];
    } catch (error) {
      console.error('Error getting user packages:', error);
      return [];
    }
  },

  async addPackage(newPackage, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for package storage');
        return false;
      }
      
      const packages = await this.getPackages(currentUser.userId, currentUser.role);
      packages.unshift(newPackage);
      await this.savePackages(packages, currentUser.userId, currentUser.role);
      return true;
    } catch (error) {
      console.error('Error adding user package:', error);
      return false;
    }
  },

  async updatePackage(packageId, updatedPackage, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for package storage');
        return false;
      }
      
      const packages = await this.getPackages(currentUser.userId, currentUser.role);
      const index = packages.findIndex(pkg => pkg.id === packageId);
      if (index !== -1) {
        packages[index] = { ...packages[index], ...updatedPackage };
        await this.savePackages(packages, currentUser.userId, currentUser.role);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating user package:', error);
      return false;
    }
  },

  async deletePackage(packageId, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for package storage');
        return false;
      }
      
      const packages = await this.getPackages(currentUser.userId, currentUser.role);
      const filteredPackages = packages.filter(pkg => pkg.id !== packageId);
      await this.savePackages(filteredPackages, currentUser.userId, currentUser.role);
      return true;
    } catch (error) {
      console.error('Error deleting user package:', error);
      return false;
    }
  },

  // Get packages for a specific user
  async getUserPackages(userId, role) {
    return await this.getPackages(userId, role);
  },

  // Get all packages from all users (admin function)
  async getAllUserPackages() {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const packageKeys = allKeys.filter(key => key.includes('PACKAGES_V1'));
      const allPackages = {};
      
      for (const key of packageKeys) {
        const packages = await AsyncStorage.getItem(key);
        if (packages) {
          allPackages[key] = JSON.parse(packages);
        }
      }
      
      return allPackages;
    } catch (error) {
      console.error('Error getting all user packages:', error);
      return {};
    }
  },
};

// Role-based User Preferences Management
export const preferencesStorage = {
  async savePreferences(preferences, role = null) {
    try {
      const currentRole = role || await getCurrentUserInfo().role;
      if (!currentRole) {
        console.error('No role specified for preferences storage');
        return false;
      }
      
      const roleKeys = getUserStorageKeys(null, currentRole);
      await AsyncStorage.setItem(roleKeys.USER_PREFERENCES, JSON.stringify(preferences));
      return true;
    } catch (error) {
      console.error('Error saving preferences:', error);
      return false;
    }
  },

  async getPreferences(role = null) {
    try {
      const currentRole = role || await getCurrentUserInfo().role;
      if (!currentRole) {
        console.error('No role specified for preferences storage');
        return {};
      }
      
      const roleKeys = getUserStorageKeys(null, currentRole);
      const preferences = await AsyncStorage.getItem(roleKeys.USER_PREFERENCES);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('Error getting preferences:', error);
      return {};
    }
  },

  async updatePreference(key, value, role = null) {
    try {
      const currentRole = role || await getCurrentUserInfo().role;
      if (!currentRole) {
        console.error('No role specified for preferences storage');
        return false;
      }
      
      const preferences = await this.getPreferences(currentRole);
      preferences[key] = value;
      await this.savePreferences(preferences, currentRole);
      return true;
    } catch (error) {
      console.error('Error updating preference:', error);
      return false;
    }
  },
};

// User-specific Notifications Management
export const userNotificationStorage = {
  async saveNotifications(notifications, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for notifications storage');
        return false;
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId, currentUser.role);
      await AsyncStorage.setItem(userKeys.NOTIFICATIONS, JSON.stringify(notifications));
      return true;
    } catch (error) {
      console.error('Error saving user notifications:', error);
      return false;
    }
  },

  async getNotifications(userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for notifications storage');
        return [];
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId, currentUser.role);
      const notifications = await AsyncStorage.getItem(userKeys.NOTIFICATIONS);
      return notifications ? JSON.parse(notifications) : [];
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return [];
    }
  },

  async addNotification(notification, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for notifications storage');
        return false;
      }
      
      const notifications = await this.getNotifications(currentUser.userId, currentUser.role);
      notifications.unshift({
        ...notification,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      });
      await this.saveNotifications(notifications, currentUser.userId, currentUser.role);
      return true;
    } catch (error) {
      console.error('Error adding user notification:', error);
      return false;
    }
  },

  async markAsRead(notificationId, userId = null, role = null) {
    try {
      const currentUser = userId ? { userId, role } : await getCurrentUserInfo();
      if (!currentUser.userId || !currentUser.role) {
        console.error('No user ID or role specified for notifications storage');
        return false;
      }
      
      const notifications = await this.getNotifications(currentUser.userId, currentUser.role);
      const index = notifications.findIndex(notif => notif.id === notificationId);
      if (index !== -1) {
        notifications[index].read = true;
        await this.saveNotifications(notifications, currentUser.userId, currentUser.role);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking user notification as read:', error);
      return false;
    }
  },
};

// User Profile Management
export const userProfileStorage = {
  async saveUserProfile(profile, userId = null) {
    try {
      const currentUser = userId ? { userId } : await getCurrentUserInfo();
      if (!currentUser.userId) {
        console.error('No user ID specified for profile storage');
        return false;
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId);
      await AsyncStorage.setItem(userKeys.USER_PROFILE, JSON.stringify(profile));
      return true;
    } catch (error) {
      console.error('Error saving user profile:', error);
      return false;
    }
  },

  async getUserProfile(userId = null) {
    try {
      const currentUser = userId ? { userId } : await getCurrentUserInfo();
      if (!currentUser.userId) {
        console.error('No user ID specified for profile storage');
        return null;
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId);
      const profile = await AsyncStorage.getItem(userKeys.USER_PROFILE);
      return profile ? JSON.parse(profile) : null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  },

  async updateUserProfile(updates, userId = null) {
    try {
      const currentUser = userId ? { userId } : await getCurrentUserInfo();
      if (!currentUser.userId) {
        console.error('No user ID specified for profile storage');
        return false;
      }
      
      const profile = await this.getUserProfile(currentUser.userId) || {};
      const updatedProfile = { ...profile, ...updates };
      await this.saveUserProfile(updatedProfile, currentUser.userId);
      return true;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return false;
    }
  },
};

// User Activity Management
export const userActivityStorage = {
  async saveUserActivity(activity, userId = null) {
    try {
      const currentUser = userId ? { userId } : await getCurrentUserInfo();
      if (!currentUser.userId) {
        console.error('No user ID specified for activity storage');
        return false;
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId);
      const existingActivity = await this.getUserActivity(currentUser.userId) || [];
      existingActivity.unshift({
        ...activity,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
      });
      
      // Keep only last 100 activities
      const limitedActivity = existingActivity.slice(0, 100);
      await AsyncStorage.setItem(userKeys.USER_ACTIVITY, JSON.stringify(limitedActivity));
      return true;
    } catch (error) {
      console.error('Error saving user activity:', error);
      return false;
    }
  },

  async getUserActivity(userId = null) {
    try {
      const currentUser = userId ? { userId } : await getCurrentUserInfo();
      if (!currentUser.userId) {
        console.error('No user ID specified for activity storage');
        return [];
      }
      
      const userKeys = getUserStorageKeys(currentUser.userId);
      const activity = await AsyncStorage.getItem(userKeys.USER_ACTIVITY);
      return activity ? JSON.parse(activity) : [];
    } catch (error) {
      console.error('Error getting user activity:', error);
      return [];
    }
  },

  async addActivity(activityType, details = {}, userId = null) {
    const activity = {
      type: activityType,
      details,
    };
    return await this.saveUserActivity(activity, userId);
  },
};

// Clear data for a specific user
export const clearUserData = async (userId, role) => {
  try {
    const userKeys = getUserStorageKeys(userId, role);
    const keysToRemove = Object.values(userKeys);
    await AsyncStorage.multiRemove(keysToRemove);
    console.log(`All data cleared for user: ${userId} (${role})`);
    return true;
  } catch (error) {
    console.error('Error clearing user data:', error);
    return false;
  }
};

// Get storage info for a specific user
export const getUserStorageInfo = async (userId, role) => {
  try {
    const userKeys = getUserStorageKeys(userId, role);
    const info = {};
    
    for (const [key, storageKey] of Object.entries(userKeys)) {
      const value = await AsyncStorage.getItem(storageKey);
      info[key] = value ? JSON.parse(value) : null;
    }
    
    return info;
  } catch (error) {
    console.error('Error getting user storage info:', error);
    return {};
  }
};

// Get all users' data (admin function)
export const getAllUsersData = async () => {
  try {
    const allKeys = await AsyncStorage.getAllKeys();
    const userData = {};
    
    // Group keys by user
    for (const key of allKeys) {
      if (key.includes('_USER_PROFILE_V1')) {
        const userId = key.split('_')[0];
        const profile = await AsyncStorage.getItem(key);
        if (profile) {
          userData[userId] = {
            profile: JSON.parse(profile),
            packages: [],
            notifications: [],
            activity: [],
          };
        }
      }
    }
    
    // Add other data for each user
    for (const userId in userData) {
      const userKeys = getUserStorageKeys(userId);
      for (const [dataType, storageKey] of Object.entries(userKeys)) {
        if (dataType !== 'USER_PROFILE') {
          const data = await AsyncStorage.getItem(storageKey);
          if (data) {
            userData[userId][dataType.toLowerCase()] = JSON.parse(data);
          }
        }
      }
    }
    
    return userData;
  } catch (error) {
    console.error('Error getting all users data:', error);
    return {};
  }
};

// App Settings Management (Global - not role-specific)
export const settingsStorage = {
  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Error saving app settings:', error);
      return false;
    }
  },

  async getSettings() {
    try {
      const settings = await AsyncStorage.getItem(STORAGE_KEYS.APP_SETTINGS);
      return settings ? JSON.parse(settings) : {
        theme: 'light',
        language: 'en',
        notifications: true,
        sound: true,
        vibration: true,
      };
    } catch (error) {
      console.error('Error getting app settings:', error);
      return {
        theme: 'light',
        language: 'en',
        notifications: true,
        sound: true,
        vibration: true,
      };
    }
  },

  async updateSetting(key, value) {
    try {
      const settings = await this.getSettings();
      settings[key] = value;
      await this.saveSettings(settings);
      return true;
    } catch (error) {
      console.error('Error updating setting:', error);
      return false;
    }
  },
};

// Clear all data for a specific role
export const clearRoleData = async (role) => {
  try {
    const roles = ['Sender/Receiver', 'Traveller', 'Agent'];
    const allKeys = [STORAGE_KEYS.USER_DATA, STORAGE_KEYS.APP_SETTINGS];
    
    // Add role-specific keys
    roles.forEach(role => {
      const roleKeys = getUserStorageKeys(null, role);
      allKeys.push(...Object.values(roleKeys));
    });
    
    await AsyncStorage.multiRemove(allKeys);
    console.log(`All data cleared for role: ${role}`);
    return true;
  } catch (error) {
    console.error('Error clearing role data:', error);
    return false;
  }
};

// Clear all data (for logout or reset)
export const clearAllData = async () => {
  try {
    const roles = ['Sender/Receiver', 'Traveller', 'Agent'];
    const allKeys = [STORAGE_KEYS.USER_DATA, STORAGE_KEYS.APP_SETTINGS];
    
    // Add role-specific keys
    roles.forEach(role => {
      const roleKeys = getUserStorageKeys(null, role);
      allKeys.push(...Object.values(roleKeys));
    });
    
    await AsyncStorage.multiRemove(allKeys);
    console.log('All app data cleared');
    return true;
  } catch (error) {
    console.error('Error clearing all data:', error);
    return false;
  }
};

// Get storage info for all roles (for debugging)
export const getStorageInfo = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const info = {};
    
    for (const key of keys) {
      const value = await AsyncStorage.getItem(key);
      info[key] = value ? JSON.parse(value) : null;
    }
    
    return info;
  } catch (error) {
    console.error('Error getting storage info:', error);
    return {};
  }
}; 