import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserStorageKeys, getCurrentUserInfo } from './storage';

// Debug utility to inspect storage
export const debugStorage = {
  // Get all storage keys
  async getAllKeys() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      console.log('🔍 All Storage Keys:', keys);
      return keys;
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  },

  // Get current user's storage info
  async getCurrentUserStorage() {
    try {
      const userInfo = await getCurrentUserInfo();
      console.log('👤 Current User Info:', userInfo);
      
      if (!userInfo.userId) {
        console.log('❌ No user logged in');
        return null;
      }

      const userKeys = getUserStorageKeys(userInfo.userId, userInfo.role);
      console.log('🗂️ User Storage Keys:', userKeys);

      const userData = {};
      for (const [key, storageKey] of Object.entries(userKeys)) {
        const value = await AsyncStorage.getItem(storageKey);
        userData[key] = value ? JSON.parse(value) : null;
      }

      console.log('📊 Current User Data:', userData);
      return userData;
    } catch (error) {
      console.error('Error getting current user storage:', error);
      return null;
    }
  },

  // Get specific user's storage
  async getUserStorage(userId, role) {
    try {
      const userKeys = getUserStorageKeys(userId, role);
      console.log(`🗂️ Storage Keys for ${userId} (${role}):`, userKeys);

      const userData = {};
      for (const [key, storageKey] of Object.entries(userKeys)) {
        const value = await AsyncStorage.getItem(storageKey);
        userData[key] = value ? JSON.parse(value) : null;
      }

      console.log(`📊 Data for ${userId}:`, userData);
      return userData;
    } catch (error) {
      console.error('Error getting user storage:', error);
      return null;
    }
  },

  // Get all users' data
  async getAllUsersData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const userKeys = keys.filter(key => key.includes('_USER_PROFILE_V1'));
      
      const allUsers = {};
      for (const key of userKeys) {
        const userId = key.split('_')[0];
        const profile = await AsyncStorage.getItem(key);
        if (profile) {
          allUsers[userId] = JSON.parse(profile);
        }
      }

      console.log('👥 All Users:', allUsers);
      return allUsers;
    } catch (error) {
      console.error('Error getting all users data:', error);
      return {};
    }
  },

  // Get storage size info
  async getStorageSize() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      let totalSize = 0;
      const keySizes = {};

      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          const size = new Blob([value]).size;
          keySizes[key] = size;
          totalSize += size;
        }
      }

      console.log('📏 Storage Size Info:', {
        totalKeys: keys.length,
        totalSize: `${(totalSize / 1024).toFixed(2)} KB`,
        keySizes
      });

      return { totalKeys: keys.length, totalSize, keySizes };
    } catch (error) {
      console.error('Error getting storage size:', error);
      return null;
    }
  },

  // Export storage data as JSON
  async exportStorageData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const data = {};

      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          data[key] = JSON.parse(value);
        }
      }

      const jsonString = JSON.stringify(data, null, 2);
      console.log('📤 Exported Storage Data:', jsonString);
      
      // For web, you can download the file
      if (typeof window !== 'undefined') {
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `storage-export-${new Date().toISOString()}.json`;
        a.click();
        URL.revokeObjectURL(url);
      }

      return data;
    } catch (error) {
      console.error('Error exporting storage data:', error);
      return null;
    }
  },

  // Clear all storage (DANGEROUS - use with caution)
  async clearAllStorage() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      await AsyncStorage.multiRemove(keys);
      console.log('🗑️ All storage cleared');
      return true;
    } catch (error) {
      console.error('Error clearing storage:', error);
      return false;
    }
  },

  // Search storage by key pattern
  async searchStorage(pattern) {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const matchingKeys = keys.filter(key => key.includes(pattern));
      
      const results = {};
      for (const key of matchingKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          results[key] = JSON.parse(value);
        }
      }

      console.log(`🔍 Search Results for "${pattern}":`, results);
      return results;
    } catch (error) {
      console.error('Error searching storage:', error);
      return {};
    }
  }
};

// Convenience functions for quick debugging
export const quickDebug = {
  // Quick check current user
  async currentUser() {
    return await debugStorage.getCurrentUserStorage();
  },

  // Quick check all keys
  async allKeys() {
    return await debugStorage.getAllKeys();
  },

  // Quick check storage size
  async size() {
    return await debugStorage.getStorageSize();
  },

  // Quick search
  async search(pattern) {
    return await debugStorage.searchStorage(pattern);
  }
};

// Usage examples:
// import { debugStorage, quickDebug } from '../utils/debugStorage';
//
// // In your component or console:
// await debugStorage.getCurrentUserStorage();
// await quickDebug.currentUser();
// await quickDebug.allKeys();
// await debugStorage.exportStorageData(); 